import React, { useEffect, useState } from 'react';
import { ChevronLeft, Play, BookOpen, List, Target } from 'lucide-react';
import Pdf from './Pdf';
import LanguageSelector from './LanguageSelector';
import ContentQuiz from './ContentQuiz';

function AiTutor() {
  const [subjects, setSubjects] = useState([]);
  const [topics, setTopics] = useState([]);
  const [subTopics, setSubTopics] = useState([]);
  const [selectorUrl, setSelectorUrl] = useState('');
  const [processId, setProcessId] = useState(null);
  const [subject, setSubject] = useState('');
  const [selectedSubjectId, setSelectedSubjectId] = useState(null);
  const [selectedTopicId, setSelectedTopicId] = useState(null);
  const [selectedSubTopicId, setSelectedSubTopicId] = useState(null);
  const [selectedSubjectName, setSelectedSubjectName] = useState('');
  const [selectedTopicName, setSelectedTopicName] = useState('');
  const [selectedSubTopicName, setSelectedSubTopicName] = useState('');
  const [showPdf, setShowPdf] = useState(false);
  const [finalPdfUrl, setFinalPdfUrl] = useState('');
  const [showQuiz, setShowQuiz] = useState(false);

  const [currentView, setCurrentView] = useState('subjects');

  useEffect(() => {
    fetch('https://sasthra.in/api/subjects')
      .then((res) => res.json())
      .then((data) => setSubjects(data))
      .catch((err) => console.error('Error fetching subjects:', err));
  }, []);

  const handleSubjectClick = (subject) => {
    setSelectedSubjectId(subject.subject_id);
    setSelectedSubjectName(subject.subject_name);
    setSelectedTopicId(null);
    setSelectedSubTopicId(null);
    setSubTopics([]);
    setSelectorUrl('');
    setShowPdf(false);
    setShowQuiz(false);
    setFinalPdfUrl('');
    setCurrentView('topics');

    fetch(`https://sasthra.in/api/topics/${subject.subject_id}`)
      .then((res) => res.json())
      .then((data) => setTopics(data))
      .catch((err) => console.error('Error fetching topics:', err));
  };

  const handleTopicClick = (topic) => {
    setSelectedTopicId(topic.topic_id);
    setSelectedTopicName(topic.topic_name);
    setSelectedSubTopicId(null);
    setSelectorUrl('');
    setShowPdf(false);
    setShowQuiz(false);
    setFinalPdfUrl('');
    setCurrentView('subtopics');

    fetch(`https://sasthra.in/api/subtopics/${topic.topic_id}`)
      .then((res) => res.json())
      .then((data) => setSubTopics(data))
      .catch((err) => console.error('Error fetching subtopics:', err));
  };

  const handleSubTopicClick = (subTopic) => {
    setSelectedSubTopicId(subTopic.sub_topic_id);
    setSelectedSubTopicName(subTopic.sub_topic_name);
    setShowPdf(false);
    setShowQuiz(false);
    setFinalPdfUrl('');
    setCurrentView('selector');

    fetch(`https://sasthra.in/api/selector-url/${subTopic.sub_topic_id}`)
      .then((res) => res.json())
      .then((data) => {
        setSubject(data.subject_name);
        console.log(data.subject_name);
        setProcessId(data.process_selector_id);
        setSelectorUrl(data.process_selector_url || 'No URL available for this subtopic.');
      })
      .catch((err) => {
        console.error('Error fetching selector URL and ID:', err);
        setSelectorUrl('Error fetching URL and ID.');
      });
  };

  const handleLanguageSelectorComplete = (url) => {
    setFinalPdfUrl(url);
    setShowPdf(true);
  };

  const handlePdfComplete = () => {
    setShowPdf(false);
    setShowQuiz(true);
  };

  const handleQuizClose = () => {
    setShowQuiz(false);
    setShowPdf(true);
  };

  const handleQuizButtonClick = () => {
    setShowPdf(false);
    setShowQuiz(true);
  };

  const handleBack = () => {
    if (currentView === 'selector') {
      setCurrentView('subtopics');
      setSelectorUrl('');
      setShowPdf(false);
      setShowQuiz(false);
      setFinalPdfUrl('');
    } else if (currentView === 'subtopics') {
      setCurrentView('topics');
      setSubTopics([]);
      setSelectedSubTopicId(null);
    } else if (currentView === 'topics') {
      setCurrentView('subjects');
      setTopics([]);
      setSelectedTopicId(null);
    }
  };

  const getBreadcrumb = () => {
    let breadcrumb = [];
    if (selectedSubjectName) breadcrumb.push(selectedSubjectName);
    if (selectedTopicName) breadcrumb.push(selectedTopicName);
    if (selectedSubTopicName) breadcrumb.push(selectedSubTopicName);
    return breadcrumb.join(' > ');
  };

return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-green-50">
      <div className="bg-white/90 backdrop-blur-sm border-b border-gray-200 shadow-sm">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {currentView !== 'subjects' && (
                <button
                  onClick={handleBack}
                  className="flex items-center space-x-2 text-gray-700 hover:text-green-600 transition-colors duration-200">
                  <ChevronLeft size={20} />
                  <span>Back</span>
                </button>
              )}
              <h1 className="text-2xl font-bold text-gray-800">
                {currentView === 'subjects' && 'Choose Your Subject'}
                {currentView === 'topics' && 'Select Topic'}
                {currentView === 'subtopics' && 'Pick Subtopic'}
                {currentView === 'selector' && (showQuiz ? 'Quiz' : showPdf ? 'Learning Content' : 'Setup Your Class')}
              </h1>
            </div>
            <div className="flex items-center space-x-2">
              {currentView === 'selector' && showPdf && (
                <button
                  onClick={handleQuizButtonClick}
                  className="flex items-center space-x-2 text-white hover:text-green-100 transition-colors duration-200 px-4 py-2 rounded-lg bg-green-500 hover:bg-green-600"
                >
                  <span>Quiz</span>
                </button>
              )}
              <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center">
                <Play size={16} className="text-white ml-0.5" />
              </div>
              <span className="text-gray-800 font-semibold">LearnStream</span>
            </div>
          </div>
          {getBreadcrumb() && <div className="mt-2 text-sm text-gray-600">{getBreadcrumb()}</div>}
        </div>
      </div>

      <div className="container mx-auto px-6 py-8">
        {currentView === 'subjects' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {subjects.map((subject) => (
              <div
                key={subject.subject_id}
                onClick={() => handleSubjectClick(subject)}
                className="group relative bg-white backdrop-blur-sm rounded-xl p-6 border border-gray-200 hover:border-green-400 transition-all duration-300 cursor-pointer hover:scale-105 hover:shadow-lg hover:shadow-green-100">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                    <BookOpen size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800 group-hover:text-green-600 transition-colors duration-200">
                      {subject.subject_name}
                    </h3>
                    <p className="text-gray-500 text-sm mt-1">Explore topics</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {currentView === 'topics' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {topics.map((topic) => (
              <div
                key={topic.topic_id}
                onClick={() => handleTopicClick(topic)}
                className="group relative bg-white backdrop-blur-sm rounded-xl p-6 border border-gray-200 hover:border-blue-400 transition-all duration-300 cursor-pointer hover:scale-105 hover:shadow-lg hover:shadow-blue-100">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                    <List size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800 group-hover:text-blue-600 transition-colors duration-200">
                      {topic.topic_name}
                    </h3>
                    <p className="text-gray-500 text-sm mt-1">View subtopics</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {currentView === 'subtopics' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {subTopics.map((subTopic) => (
              <div
                key={subTopic.sub_topic_id}
                onClick={() => handleSubTopicClick(subTopic)}
                className="group relative bg-white backdrop-blur-sm rounded-xl p-6 border border-gray-200 hover:border-purple-400 transition-all duration-300 cursor-pointer hover:scale-105 hover:shadow-lg hover:shadow-purple-100">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                    <Target size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800 group-hover:text-purple-600 transition-colors duration-200">
                      {subTopic.sub_topic_name}
                    </h3>
                    <p className="text-gray-500 text-sm mt-1">Start learning</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {currentView === 'selector' && (
          <div className="max-w-7xl mx-auto">
            {!showPdf && !showQuiz ? (
              <div className="flex justify-center">
                <LanguageSelector
                  processId={processId}
                  subject={subject}
                  onComplete={handleLanguageSelectorComplete}
                />
              </div>
            ) : showQuiz ? (
              <ContentQuiz processId={processId} onClose={handleQuizClose} />
            ) : (
              selectorUrl && selectorUrl.startsWith('http') ? (
                <Pdf
                  fileUrl={selectorUrl}
                  fileName={
                    selectedSubTopicName
                      ? `${selectedSubTopicName.replace(/\s+/g, '_')}.pdf`
                      : selectorUrl.split('/').pop()
                  }
                  subjectName={selectedSubjectName}
                  processSelectorId={processId}
                  onComplete={handlePdfComplete}
                />
              ) : (
                <div className="bg-white backdrop-blur-sm rounded-xl p-8 border border-gray-200 shadow-sm">
                  <div className="text-center mb-6">
                    <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Play size={32} className="text-white ml-1" />
                    </div>
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">No Content Available</h2>
                    <p className="text-gray-500">Unable to load learning content</p>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                      <Target className="mr-2" size={20} />
                      Process Selector URL
                    </h3>
                    <div className="text-gray-600 italic">{selectorUrl}</div>
                  </div>
                </div>
              )
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default AiTutor;