// import { createSlice } from "@reduxjs/toolkit";
// import { directorApi } from "../../../../redux/api/api";

// const initialState = {
//   students: [],
// };

// export const studentSlice = directorApi.injectEndpoints({
//   endpoints: (builder) => ({
//     getStudents: builder.query({
//       query: () => ({
//         url: "/student-dashboard",
//         method: "GET",
//       }),
//       transformResponse: (response) => {
//         console.log("Get Students Response:", response);
//         return response.students || [];
//       },
//       transformErrorResponse: ({ status, data }) => {
//         console.log("Get Students Error:", { status, data });
//         return { status, data };
//       },
//       providesTags: ["Students"],
//     }),
//   }),
// });

// const studentsSlice = createSlice({
//   name: "students",
//   initialState,
//     reducers: {},
//   setsubjects: (state, action) => {
//     state.subjects = action.payload;
//   }
// });

// export default studentsSlice.reducer;
// export const { setsubjects} = studentsSlice.actions;

// export const { useGetStudentsQuery } = studentSlice;

import { createSlice } from '@reduxjs/toolkit';
import { directorApi } from '../../../../redux/api/api';

const initialState = {
  students: [],
  course: []
};

export const studentSlice = directorApi.injectEndpoints({
  endpoints: (builder) => ({
    getStudents: builder.query({
      query: () => ({
        url: '/student-dashboard',
        method: 'GET'
      }),
      transformResponse: (response) => {
        console.log('Get Students Response:', response);
        return {
          student: response.student || {},
          kota_teachers: response.kota_teachers || []
        };
      },
      transformErrorResponse: (error) => {
        console.error('Get Students Error:', error);
        return error;
      },
      providesTags: ['Students']
    }),
    getParents: builder.query({
      query: () => ({
        url: '/parent-dashboard',
        method: 'GET'
      }),
      transformResponse: (response) => {
        console.log('Get Parents Response:', response);
        return response;
      },
      transformErrorResponse: (error) => {
        console.error('Get parent Error:', error);
        return error;
      },
      providesTags: ['Students']
    })
  })
});

const studentsSlice = createSlice({
  name: 'students',
  initialState,
  reducers: {
    setcourse: (state, action) => {
      state.course = action.payload;
    }
  }
});

export default studentsSlice.reducer;
export const { setcourse } = studentsSlice.actions;
export const { useGetStudentsQuery, useGetParentsQuery } = studentSlice;
