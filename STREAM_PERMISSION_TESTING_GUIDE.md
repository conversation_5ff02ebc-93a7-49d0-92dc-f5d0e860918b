# Stream Permission System - Testing Guide

## 🚀 Backend Changes Completed

### ✅ What Was Added to app.py:

1. **Database Table Creation** - `stream_permissions` table
2. **Permission Check** - Modified `/api/livekit/join` endpoint
3. **New API Endpoints**:
   - `POST /api/stream-permission-request` - Student requests permission
   - `POST /api/stream-permission-response` - Faculty approves/rejects
   - `GET /api/stream-permission-status/<student_id>/<session_id>` - Check status
   - `GET /api/stream-permission-requests/<faculty_id>` - Get faculty requests
4. **Socket.IO Events** - Real-time notifications
5. **Enhanced Active Streams** - Added teacher names

## 📋 Testing Prerequisites

### 1. Start Your Backend Server
```bash
cd c:\Users\<USER>\production\sasthra-01-07-2025\sasthra-ui
python app.py
```

### 2. Import Postman Collection
- Import the `Stream_Permission_API_Tests.postman_collection.json` file
- Set environment variables in Postman:
  - `base_url`: `http://localhost:8012`
  - `session_id`: `test_session_123`
  - `student_id`: `student_123`
  - `faculty_id`: `faculty_456`
  - `teacher_id`: `teacher_789`

## 🧪 Step-by-Step Testing Process

### Step 1: Check Active Streams (Initially Empty)
**Request:** `GET /active-streams`
**Expected Response:**
```json
{
  "success": true,
  "active_streams": [],
  "total_streams": 0
}
```

### Step 2: Start a Test Stream (Simulate Teacher)
**Request:** `POST /api/enhanced-stream/start`
**Body:**
```json
{
  "session_id": "test_session_123",
  "teacher_id": "teacher_789",
  "teacher_name": "Test Teacher",
  "quality": "medium"
}
```
**Expected Response:**
```json
{
  "success": true,
  "session_id": "test_session_123",
  "livekit_token": "...",
  "livekit_url": "...",
  "roomName": "test_session_123"
}
```

### Step 3: Verify Stream is Active
**Request:** `GET /active-streams`
**Expected Response:**
```json
{
  "success": true,
  "active_streams": [
    {
      "session_id": "test_session_123",
      "teacher_id": "teacher_789",
      "teacher_name": "Test Teacher",
      "viewer_count": 0,
      "quality": "medium",
      "created_at": "2025-01-18T...",
      "uptime": 5.2,
      "features": {
        "chat_enabled": true,
        "recording_enabled": true,
        "screen_sharing": true
      }
    }
  ],
  "total_streams": 1
}
```

### Step 4: Student Tries to Join Without Permission (Should Fail)
**Request:** `POST /api/livekit/join`
**Body:**
```json
{
  "session_id": "test_session_123",
  "user_id": "student_123",
  "user_name": "Test Student",
  "user_role": "student"
}
```
**Expected Response (403 Forbidden):**
```json
{
  "message": "Permission required to join this stream. Please request permission from your faculty.",
  "permission_required": true
}
```

### Step 5: Student Requests Permission
**Request:** `POST /api/stream-permission-request`
**Body:**
```json
{
  "session_id": "test_session_123",
  "student_id": "student_123",
  "faculty_id": "faculty_456",
  "message": "Please allow me to join the Physics live class. I missed the center session today."
}
```
**Expected Response:**
```json
{
  "success": true,
  "request_id": "uuid-generated-id",
  "message": "Permission request sent successfully",
  "status": "pending"
}
```
**📝 Copy the `request_id` from response and set it as Postman variable**

### Step 6: Check Permission Status (Should be Pending)
**Request:** `GET /api/stream-permission-status/student_123/test_session_123`
**Expected Response:**
```json
{
  "status": "pending",
  "can_join": false,
  "request_message": "Please allow me to join the Physics live class...",
  "response_message": null,
  "requested_at": "2025-01-18T...",
  "responded_at": null
}
```

### Step 7: Faculty Checks Pending Requests
**Request:** `GET /api/stream-permission-requests/faculty_456`
**Expected Response:**
```json
{
  "success": true,
  "requests": [
    {
      "request_id": "uuid-generated-id",
      "session_id": "test_session_123",
      "student_id": "student_123",
      "student_name": "Student student_123",
      "teacher_name": "Test Teacher",
      "stream_title": "Test Teacher's Live Class",
      "message": "Please allow me to join the Physics live class...",
      "requested_at": "2025-01-18T...",
      "stream_info": {
        "viewer_count": 0,
        "quality": "medium",
        "uptime": 45.6
      }
    }
  ],
  "total_pending": 1
}
```

### Step 8: Faculty Approves Request
**Request:** `POST /api/stream-permission-response`
**Body:**
```json
{
  "request_id": "uuid-from-step-5",
  "action": "approve",
  "faculty_id": "faculty_456",
  "response_message": "Approved! You can join the stream now."
}
```
**Expected Response:**
```json
{
  "success": true,
  "message": "Request approved successfully",
  "action": "approved",
  "student_name": "Student student_123"
}
```

### Step 9: Check Permission Status (Should be Approved)
**Request:** `GET /api/stream-permission-status/student_123/test_session_123`
**Expected Response:**
```json
{
  "status": "approved",
  "can_join": true,
  "request_message": "Please allow me to join the Physics live class...",
  "response_message": "Approved! You can join the stream now.",
  "requested_at": "2025-01-18T...",
  "responded_at": "2025-01-18T..."
}
```

### Step 10: Student Joins Stream (Should Work Now)
**Request:** `POST /api/livekit/join`
**Body:**
```json
{
  "session_id": "test_session_123",
  "user_id": "student_123",
  "user_name": "Test Student",
  "user_role": "student"
}
```
**Expected Response:**
```json
{
  "success": true,
  "token": "livekit-token-for-student",
  "livekit_url": "wss://...",
  "room_name": "test_session_123",
  "stream_info": {
    "session_id": "test_session_123",
    "teacher_id": "teacher_789",
    "viewer_count": 1
  }
}
```

### Step 11: Faculty Joins Stream (Should Work Without Permission)
**Request:** `POST /api/livekit/join`
**Body:**
```json
{
  "session_id": "test_session_123",
  "user_id": "faculty_456",
  "user_name": "Test Faculty",
  "user_role": "faculty"
}
```
**Expected Response:** Should work immediately without permission check.

## 🔍 Testing Edge Cases

### Test 1: Duplicate Request
- Try Step 5 again with same student/session
- Should return: "Your request is already pending approval"

### Test 2: Request for Non-existent Stream
- Change session_id to "non_existent_stream"
- Should return: "Stream not found"

### Test 3: Reject Permission
- Use action: "reject" in Step 8
- Student should not be able to join

### Test 4: Faculty Authorization
- Try to approve with wrong faculty_id
- Should return: "Request not found or you are not authorized"

## 🎯 Success Criteria

✅ **All tests should pass with expected responses**
✅ **Students cannot join without permission**
✅ **Faculty can approve/reject requests**
✅ **Real-time notifications work (check server logs)**
✅ **Database table is created automatically**
✅ **Permission system doesn't affect faculty/teacher access**

## 🐛 Troubleshooting

### Database Issues
- Check if MySQL/database is running
- Verify database connection in shared/database.py
- Check server logs for table creation errors

### Permission Errors
- Ensure request_id is correctly copied from Step 5
- Verify all required fields are provided
- Check user roles and IDs

### Server Errors
- Check server logs for detailed error messages
- Verify all dependencies are installed
- Ensure port 8012 is available

## 📝 Next Steps After Testing

Once backend testing is complete:
1. ✅ Backend API working
2. 🔄 Frontend integration (LiveStreaming.jsx enhancement)
3. 🔄 Faculty panel updates (CenterTraineeLiveViewer.jsx)
4. 🔄 Real-time Socket.IO integration
5. 🔄 End-to-end testing
