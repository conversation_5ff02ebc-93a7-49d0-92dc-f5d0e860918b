import { useState } from "react"
import PerformanceBasedDashboard from "./PerformanceBasedDashboard"
import TestPerformanceDashboard from "./TestPerformanceDashboard"
import TestBreakdownDashboard from "./TestBreakdownDashboard"
import TimeAnalysisDashboard from "./TimeAnalysisDashboard"

const NewDashboard = () => {
  const [activeTab, setActiveTab] = useState("performance")

  const renderDashboardContent = (tabValue) => {
    switch (tabValue) {
      case "performance":
        return <PerformanceBasedDashboard />
      case "test-performance":
        return <TestPerformanceDashboard />
      case "test-breakdown":
        return <TestBreakdownDashboard />
      case "time-analysis":
        return <TimeAnalysisDashboard />
      default:
        return <PerformanceBasedDashboard />
    }
  }

  const tabItems = [
    {
      value: "performance",
      label: "Performance Based",
      shortLabel: "Performance",
    },
    {
      value: "test-performance",
      label: "Test Performance",
      shortLabel: "Test Perf",
    },
    {
      value: "test-breakdown",
      label: "Test Breakdown",
      shortLabel: "Breakdown",
    },
    {
      value: "time-analysis",
      label: "Time Analysis",
      shortLabel: "Time",
    },
  ]

  const handleKeyDown = (event, tabValue) => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault()
      setActiveTab(tabValue)
    }
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight ">Dashboard Overview</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Monitor and analyze your performance metrics across different views
        </p>
      </div>

      <div className="w-full">
        {/* Custom Tab Navigation */}
        <div className="grid w-full grid-cols-2 md:grid-cols-4 bg-gray-100 p-1 rounded-lg shadow-sm">
          {tabItems.map((tab) => (
            <button
              key={tab.value}
              onClick={() => setActiveTab(tab.value)}
              onKeyDown={(e) => handleKeyDown(e, tab.value)}
              className={`
                py-3 px-4 text-sm font-medium rounded-md transition-all duration-200 
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                ${
                  activeTab === tab.value
                    ? "bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm border border-gray-200 dark:border-gray-600"
                    : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700/50"
                }
              `}
              role="tab"
              aria-selected={activeTab === tab.value}
              aria-controls={`tabpanel-${tab.value}`}
              tabIndex={activeTab === tab.value ? 0 : -1}
            >
              <span className="hidden sm:inline">{tab.label}</span>
              <span className="sm:hidden">{tab.shortLabel}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="mt-8">
          {tabItems.map((tab) => (
            <div
              key={tab.value}
              id={`tabpanel-${tab.value}`}
              role="tabpanel"
              aria-labelledby={`tab-${tab.value}`}
              className={`
                ${activeTab === tab.value ? "block" : "hidden"}
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg
                animate-in fade-in-50 duration-200
              `}
              tabIndex={0}
            >
              {renderDashboardContent(tab.value)}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default NewDashboard
