'use client';

import { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  useListBatchStudentsQuery,
  useGetStudentEvaluationsQuery
} from './evaluationResponse.Slice';
import {
  FaUsers,
  FaGraduationCap,
  FaCalendarPlus,
  FaUser,
  FaChartLine,
  FaEyeSlash,
  FaChartBar,
  FaTimes,
  FaExclamationTriangle,
  FaCheck,
  FaLightbulb,
  FaCheckCircle,
  FaUserEdit,
  FaThumbsUp,
  FaSearch,
  FaArrowUp,
  FaClipboardList
} from 'react-icons/fa';
import { BiSolidCalendarEdit } from 'react-icons/bi';

const StudentList = () => {
  const { data: batches, isLoading, isError, error } = useListBatchStudentsQuery();
  const [selectedStudentId, setSelectedStudentId] = useState(null);
  const [selectedTestId, setSelectedTestId] = useState(null);

  const {
    data: evaluations,
    isLoading: isEvalLoading,
    isFetching,
    isError: isEvalError,
    error: evalError
  } = useGetStudentEvaluationsQuery(selectedStudentId, { skip: !selectedStudentId });

  const handleResultClick = useCallback(
    (studentId) => {
      console.log('handleResultClick called with studentId:', studentId);
      if (studentId === selectedStudentId) {
        console.log('Closing modal, resetting states');
        setSelectedStudentId(null);
        setSelectedTestId(null);
      } else {
        console.log('Opening modal for studentId:', studentId);
        setSelectedStudentId(studentId);
        setSelectedTestId(null);
      }
    },
    [selectedStudentId]
  );

  const closeModal = () => {
    console.log('Closing modal');
    setSelectedStudentId(null);
    setSelectedTestId(null);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6, staggerChildren: 0.1 } }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 15 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } }
  };

  const modalVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { opacity: 1, scale: 1, transition: { duration: 0.3, ease: 'easeOut' } },
    exit: { opacity: 0, scale: 0.8, transition: { duration: 0.2 } }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex flex-col items-center space-y-4">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }}
            className="w-8 h-8 border-3 border-[#F59E0B] border-t-transparent rounded-full"
          />
          <p className="text-lg text-gray-600 font-medium">Loading students...</p>
        </motion.div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex justify-center items-center h-screen">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center p-8 border-2 border-red-200 rounded-2xl">
          <FaExclamationTriangle className="text-4xl text-red-500 mb-4" />
          <p className="text-lg text-red-600 font-medium">
            Error: {error?.data?.message || 'Failed to load students'}
          </p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-6">
      <div className="container mx-auto max-w-6xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            <FaUsers className="inline-block text-[#F59E0B] mr-4" />
            Student Batches
          </h1>
          <p className="text-xl text-gray-600 font-light">
            Manage and view student evaluations across all batches
          </p>
        </motion.div>

        {/* Batches List */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-8">
          {batches?.batches?.map((batch) => (
            <motion.div
              key={batch.batch_id}
              variants={itemVariants}
              className="border-2 border-gray-200 rounded-2xl p-8 hover:border-[#F59E0B]/30 hover:shadow-lg transition-all duration-300">
              {/* Batch Header */}
              <div className="mb-6 pb-4 border-b border-gray-100">
                <div className="flex items-center mb-3">
                  <motion.div
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    className="w-12 h-12 rounded-xl border-2 border-[#F59E0B] flex items-center justify-center mr-4">
                    <FaGraduationCap className="text-[#F59E0B] text-xl" />
                  </motion.div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-800">{batch.batch_name}</h2>
                    <p className="text-gray-600 mt-1">{batch.description}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-6 text-sm text-gray-500">
                  <span className="flex items-center">
                    <FaCalendarPlus className="text-[#F59E0B] mr-2" />
                    Created: {new Date(batch.created_at).toLocaleDateString()}
                  </span>
                  <span className="flex items-center">
                    <BiSolidCalendarEdit className="text-[#F59E0B] mr-2" />
                    Updated: {new Date(batch.updated_at).toLocaleDateString()}
                  </span>
                </div>
              </div>

              {/* Students Grid */}
              <div className="grid gap-4">
                {batch.students.map((student, index) => (
                  <motion.div
                    key={student.student_id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex justify-between items-center p-4 border border-gray-200 rounded-xl hover:border-[#F59E0B]/50 hover:shadow-md transition-all duration-300 group">
                    <div className="flex items-center">
                      <motion.div
                        whileHover={{ scale: 1.1 }}
                        className="w-10 h-10 rounded-full border-2 border-gray-300 flex items-center justify-center mr-4 group-hover:border-[#F59E0B] transition-colors duration-300">
                        <FaUser className="text-gray-500 group-hover:text-[#F59E0B] transition-colors duration-300" />
                      </motion.div>
                      <div>
                        <span className="text-lg font-semibold text-gray-800">
                          {student.first_name}
                        </span>
                        <p className="text-sm text-gray-500">Student ID: {student.student_id}</p>
                      </div>
                    </div>

                    <motion.button
                      onClick={() => handleResultClick(student.student_id)}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      disabled={isEvalLoading || isFetching}
                      className={`px-6 py-3 border-2 border-[#F59E0B] text-[#F59E0B] rounded-xl hover:text-white hover:bg-[#F59E0B] transition-all duration-300 font-semibold flex items-center space-x-2 pointer-events-auto ${
                        isEvalLoading || isFetching ? 'opacity-50 cursor-not-allowed' : ''
                      }`}>
                      {isEvalLoading || isFetching ? (
                        <span>Loading...</span>
                      ) : (
                        <>
                          {selectedStudentId === student.student_id ? (
                            <FaEyeSlash />
                          ) : (
                            <FaChartLine />
                          )}
                          <span>
                            {selectedStudentId === student.student_id
                              ? 'Hide Results'
                              : 'View Results'}
                          </span>
                        </>
                      )}
                    </motion.button>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Modal */}
        <AnimatePresence>
          {selectedStudentId && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm flex justify-center items-center z-50 p-4"
              onClick={closeModal}>
              <motion.div
                variants={modalVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                onClick={(e) => e.stopPropagation()}
                className="border-2 border-gray-200 rounded-3xl p-8 max-w-5xl w-full max-h-[85vh] overflow-y-auto shadow-2xl"
                style={{ backgroundColor: 'white' }}>
                {/* Modal Header */}
                <div className="flex justify-between items-center mb-8 pb-4 border-b border-gray-100">
                  <div className="flex items-center">
                    <motion.div
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.6 }}
                      className="w-12 h-12 rounded-xl border-2 border-[#F59E0B] flex items-center justify-center mr-4">
                      <FaChartBar className="text-[#F59E0B] text-xl" />
                    </motion.div>
                    <div>
                      <h2 className="text-3xl font-bold text-gray-800">Evaluation Results</h2>
                      <p className="text-gray-600 mt-1">
                        Detailed performance analysis for Student ID: {selectedStudentId}
                      </p>
                    </div>
                  </div>
                  <motion.button
                    onClick={closeModal}
                    whileHover={{ scale: 1.1, rotate: 90 }}
                    whileTap={{ scale: 0.9 }}
                    className="w-10 h-10 rounded-full border-2 border-gray-300 flex items-center justify-center text-gray-500 hover:border-[#F59E0B] hover:text-[#F59E0B] transition-all duration-300">
                    <FaTimes />
                  </motion.button>
                </div>

                {/* Modal Content */}
                {isEvalLoading || isFetching ? (
                  <div className="text-center py-12">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }}
                      className="w-8 h-8 border-3 border-[#F59E0B] border-t-transparent rounded-full mx-auto mb-4"
                    />
                    <p className="text-gray-600 font-medium">Loading evaluations...</p>
                  </div>
                ) : isEvalError || !evaluations?.batches ? (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center py-12">
                    <FaClipboardList className="text-6xl text-gray-300 mb-4" />
                    <p className="text-xl text-gray-600 font-medium">
                      This student hasn't attended any tests
                    </p>
                    <p className="text-gray-500 mt-2">
                      Test evaluations will appear here once the student attends a test
                    </p>
                  </motion.div>
                ) : (
                  (() => {
                    const selectedBatch = evaluations.batches.find((b) =>
                      b.students.some((s) => s.student_id === selectedStudentId)
                    );
                    const selectedStudent = selectedBatch?.students.find(
                      (s) => s.student_id === selectedStudentId
                    );

                    return selectedStudent?.evaluations?.length > 0 ? (
                      <div className="space-y-6">
                        {/* Test Count and List */}
                        <div className="p-4 border border-gray-200 rounded-xl">
                          <h3 className="text-xl font-bold text-gray-800 mb-4">
                            <FaClipboardList className="inline-block mr-2" />
                            Tests Attended: {selectedStudent.evaluations.length}
                          </h3>
                          <div className="grid gap-4">
                            {selectedStudent.evaluations.map((evaluation, index) => (
                              <motion.div
                                key={evaluation.test_id}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.1 }}
                                onClick={() => {
                                  console.log('Selected test_id:', evaluation.test_id);
                                  setSelectedTestId(evaluation.test_id);
                                }}
                                className={`p-4 border rounded-xl cursor-pointer transition-all duration-300 ${
                                  selectedTestId === evaluation.test_id
                                    ? 'border-[#F59E0B] bg-[#F59E0B]/10'
                                    : 'border-gray-200 hover:border-[#F59E0B]/50'
                                }`}>
                                <div className="flex justify-between items-center">
                                  <div>
                                    <p className="font-semibold text-gray-800">
                                      Test ID: {evaluation.test_id}
                                    </p>
                                    <p className="text-sm text-gray-600">
                                      Exam: {evaluation.overall_feedback.exam}
                                    </p>
                                    <p className="text-sm text-gray-500">
                                      Evaluated:{' '}
                                      {new Date(
                                        evaluation.overall_feedback.evaluation_timestamp
                                      ).toLocaleString()}
                                    </p>
                                  </div>
                                  <FaSearch className="text-[#F59E0B]" />
                                </div>
                              </motion.div>
                            ))}
                          </div>
                        </div>

                        {/* Feedback for Selected Test */}
                        {selectedTestId ? (
                          (() => {
                            const selectedEvaluation = selectedStudent.evaluations.find(
                              (evaluation) => evaluation.test_id === selectedTestId
                            );
                            return selectedEvaluation?.performance_analysis?.length > 0 ? (
                              <motion.div
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                transition={{ delay: 0.2 }}
                                className="space-y-6">
                                {/* Overall Feedback */}
                                <div className="p-4 border border-gray-200 rounded-xl">
                                  <h3 className="text-xl font-bold text-gray-800 mb-4">
                                    Overall Feedback for Test ID: {selectedTestId}
                                  </h3>
                                  <div className="space-y-4">
                                    <div>
                                      <p className="font-semibold text-gray-800">
                                        Performance Snapshot:
                                      </p>
                                      <p className="text-gray-600">
                                        {selectedEvaluation.overall_feedback.performance_snapshot ||
                                          'N/A'}
                                      </p>
                                    </div>
                                    <div>
                                      <p className="font-semibold text-gray-800">
                                        Conceptual Strengths:
                                      </p>
                                      <ul className="list-disc pl-5 text-gray-600">
                                        {selectedEvaluation.overall_feedback.conceptual_strengths?.map(
                                          (strength, index) => <li key={index}>{strength}</li>
                                        ) || <p>N/A</p>}
                                      </ul>
                                    </div>
                                    <div>
                                      <p className="font-semibold text-gray-800">
                                        Primary Area for Improvement:
                                      </p>
                                      <p className="text-gray-600">
                                        {selectedEvaluation.overall_feedback
                                          .primary_area_for_improvement || 'N/A'}
                                      </p>
                                    </div>
                                    <div>
                                      <p className="font-semibold text-gray-800">
                                        Strategic Action Plan:
                                      </p>
                                      <ul className="list-disc pl-5 text-gray-600">
                                        {selectedEvaluation.overall_feedback.strategic_action_plan?.map(
                                          (action, index) => (
                                            <li key={index}>
                                              {action.action} (Focus: {action.focus}, Priority:{' '}
                                              {action.priority})
                                            </li>
                                          )
                                        ) || <p>N/A</p>}
                                      </ul>
                                    </div>
                                  </div>
                                </div>
                                {/* Question-by-Question Feedback */}
                                {selectedEvaluation.performance_analysis.map(
                                  (evaluation, index) => (
                                    <motion.div
                                      key={index}
                                      initial={{ opacity: 0, y: 20 }}
                                      animate={{ opacity: 1, y: 0 }}
                                      transition={{ delay: index * 0.1 }}
                                      className="border border-gray-200 rounded-2xl p-6 hover:border-[#F59E0B]/30 hover:shadow-md transition-all duration-300">
                                      <div className="flex items-center mb-4 pb-3 border-b border-gray-100">
                                        <motion.div
                                          whileHover={{ scale: 1.1 }}
                                          className="w-10 h-10 rounded-full border-2 border-[#F59E0B] flex items-center justify-center mr-3">
                                          <span className="text-[#F59E0B] font-bold">
                                            {evaluation.question_number}
                                          </span>
                                        </motion.div>
                                        <h3 className="text-xl font-bold text-gray-800">
                                          Question {evaluation.question_number}
                                        </h3>
                                        <div className="ml-auto flex items-center space-x-3">
                                          <span
                                            className={`px-3 py-1 rounded-full text-sm font-semibold ${
                                              evaluation.is_correct
                                                ? 'text-green-700 border border-green-200'
                                                : 'text-red-700 border border-red-200'
                                            }`}>
                                            {evaluation.is_correct ? (
                                              <FaCheck className="inline-block mr-1" />
                                            ) : (
                                              <FaTimes className="inline-block mr-1" />
                                            )}
                                            {evaluation.is_correct ? 'Correct' : 'Incorrect'}
                                          </span>
                                          <span className="px-3 py-1 rounded-full text-sm font-semibold text-[#F59E0B] border border-[#F59E0B]/30">
                                            {evaluation.marks_awarded ?? 'N/A'} marks
                                          </span>
                                        </div>
                                      </div>
                                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                        <div className="space-y-4">
                                          <div className="flex items-start space-x-3">
                                            <FaLightbulb className="text-[#F59E0B] mt-1" />
                                            <div>
                                              <p className="font-semibold text-gray-800 mb-1">
                                                Core Concept
                                              </p>
                                              <p className="text-gray-600">
                                                {evaluation.feedback?.core_concept_tested || 'N/A'}
                                              </p>
                                            </div>
                                          </div>
                                          <div className="flex items-start space-x-3">
                                            <FaCheckCircle className="text-green-500 mt-1" />
                                            <div>
                                              <p className="font-semibold text-gray-800 mb-1">
                                                Correct Answer
                                              </p>
                                              <p className="text-gray-600">
                                                {evaluation.expert_calculated_answer || 'N/A'}
                                              </p>
                                            </div>
                                          </div>
                                          <div className="flex items-start space-x-3">
                                            <FaUserEdit className="text-blue-500 mt-1" />
                                            <div>
                                              <p className="font-semibold text-gray-800 mb-1">
                                                Student Answer
                                              </p>
                                              <p className="text-gray-600">
                                                {evaluation.student_final_answer || 'N/A'}
                                              </p>
                                            </div>
                                          </div>
                                          {evaluation.error_type && (
                                            <div className="flex items-start space-x-3">
                                              <FaExclamationTriangle className="text-red-500 mt-1" />
                                              <div>
                                                <p className="font-semibold text-gray-800 mb-1">
                                                  Error Type
                                                </p>
                                                <p className="text-gray-600">
                                                  {evaluation.error_type}
                                                </p>
                                              </div>
                                            </div>
                                          )}
                                        </div>
                                        <div className="space-y-4">
                                          {evaluation.feedback?.positive_feedback && (
                                            <div className="p-4 border border-green-200 rounded-xl">
                                              <div className="flex items-start space-x-3">
                                                <FaThumbsUp className="text-green-500 mt-1" />
                                                <div>
                                                  <p className="font-semibold text-gray-800 mb-2">
                                                    Positive Feedback
                                                  </p>
                                                  <p className="text-gray-600">
                                                    {evaluation.feedback.positive_feedback}
                                                  </p>
                                                </div>
                                              </div>
                                            </div>
                                          )}
                                          {evaluation.feedback?.error_analysis && (
                                            <div className="p-4 border border-red-200 rounded-xl">
                                              <div className="flex items-start space-x-3">
                                                <FaSearch className="text-red-500 mt-1" />
                                                <div>
                                                  <p className="font-semibold text-gray-800 mb-2">
                                                    Error Analysis
                                                  </p>
                                                  <p className="text-gray-600">
                                                    {evaluation.feedback.error_analysis}
                                                  </p>
                                                </div>
                                              </div>
                                            </div>
                                          )}
                                          {evaluation.feedback?.improvement_suggestion && (
                                            <div className="p-4 border border-[#F59E0B]/30 rounded-xl">
                                              <div className="flex items-start space-x-3">
                                                <FaArrowUp className="text-[#F59E0B] mt-1" />
                                                <div>
                                                  <p className="font-semibold text-gray-800 mb-2">
                                                    Improvement Suggestion
                                                  </p>
                                                  <p className="text-gray-600">
                                                    {evaluation.feedback.improvement_suggestion}
                                                  </p>
                                                </div>
                                              </div>
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    </motion.div>
                                  )
                                )}
                              </motion.div>
                            ) : (
                              <p className="text-gray-600">
                                No performance analysis available for this test.
                              </p>
                            );
                          })()
                        ) : (
                          <p className="text-gray-600">Please select a test to view feedback.</p>
                        )}
                      </div>
                    ) : (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-center py-12">
                        <FaClipboardList className="text-6xl text-gray-300 mb-4" />
                        <p className="text-xl text-gray-600 font-medium">
                          No evaluations available for this student (ID: {selectedStudentId})
                        </p>
                        <p className="text-gray-500 mt-2">
                          Evaluations will appear here once they are completed
                        </p>
                      </motion.div>
                    );
                  })()
                )}
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default StudentList;
