import React, { useEffect, useState } from 'react';
import { X, BarChart3, Users, Activity, Trophy, Clock, Target, TrendingUp } from 'lucide-react';

const FLASK_API_URL = 'https://sasthra.in';

const EngagementDashboard = ({ quizId, onClose }) => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${FLASK_API_URL}/content/engagement_dashboard?quiz_id=${quizId}`);
        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.error || `Server error ${response.status}`);
        }
        
        setDashboardData(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (quizId) {
      fetchDashboardData();
    }
  }, [quizId]);

  if (loading) {
    return (
      <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-8 border border-white/10 max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-white flex items-center gap-2">
            <BarChart3 size={24} />
            Engagement Dashboard
          </h2>
          <button onClick={onClose} className="text-white hover:text-blue-400">
            <X size={24} />
          </button>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="text-white text-lg">Loading dashboard data...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gradient-to-br from-white to-gray-50 backdrop-blur-sm rounded-xl p-8 border border-green-200 max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
            <BarChart3 size={24} />
            Engagement Dashboard
          </h2>
          <button onClick={onClose} className="text-gray-800 hover:text-green-600">
            <X size={24} />
          </button>
        </div>
        <div className="text-center text-red-600 p-4">
          <p>Error loading dashboard: {error}</p>
        </div>
      </div>
    );
  }

  const getPerformanceColor = (percentage) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-green-500';
    if (percentage >= 40) return 'text-green-400';
    return 'text-red-600';
  };

  const getPerformanceMessage = (percentage) => {
    if (percentage >= 80) return 'Excellent';
    if (percentage >= 60) return 'Good';
    if (percentage >= 40) return 'Average';
    return 'Needs Improvement';
  };

  return (
    <div className="bg-gradient-to-br from-white to-gray-50 backdrop-blur-sm rounded-xl p-8 border border-green-200 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
          <BarChart3 size={24} />
          Engagement Dashboard
        </h2>
        <button onClick={onClose} className="text-gray-800 hover:text-green-600">
          <X size={24} />
        </button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg p-6 border border-green-200 shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <Users className="text-green-600" size={24} />
            <h3 className="text-lg font-semibold text-gray-800">Active Students</h3>
          </div>
          <div className="text-3xl font-bold text-gray-800">{dashboardData?.active_students || 0}</div>
          <div className="text-sm text-gray-600">Currently participating</div>
        </div>

        <div className="bg-white rounded-lg p-6 border border-green-200 shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <Clock className="text-green-600" size={24} />
            <h3 className="text-lg font-semibold text-gray-800">Avg Response Time</h3>
          </div>
          <div className="text-3xl font-bold text-gray-800">{dashboardData?.avg_response_time?.toFixed(1) || 0}s</div>
          <div className="text-sm text-gray-600">Per question</div>
        </div>

        <div className="bg-white rounded-lg p-6 border border-green-200 shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <Target className="text-green-600" size={24} />
            <h3 className="text-lg font-semibold text-gray-800">Overall Accuracy</h3>
          </div>
          <div className={`text-3xl font-bold ${getPerformanceColor(dashboardData?.overall_accuracy || 0)}`}>
            {dashboardData?.overall_accuracy?.toFixed(1) || 0}%
          </div>
          <div className="text-sm text-gray-600">{getPerformanceMessage(dashboardData?.overall_accuracy || 0)}</div>
        </div>

        <div className="bg-white rounded-lg p-6 border border-green-200 shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <Activity className="text-green-600" size={24} />
            <h3 className="text-lg font-semibold text-gray-800">Total Responses</h3>
          </div>
          <div className="text-3xl font-bold text-gray-800">{dashboardData?.total_answers_recorded || 0}</div>
          <div className="text-sm text-gray-600">Answers recorded</div>
        </div>
      </div>

      {/* Leaderboard */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div className="bg-white rounded-lg p-6 border border-green-200 shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <Trophy className="text-green-600" size={24} />
            <h3 className="text-lg font-semibold text-gray-800">Leaderboard</h3>
          </div>
          <div className="space-y-3">
            {dashboardData?.leaderboard?.map((student, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-green-100">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold">
                    {student.rank}
                  </div>
                  <div>
                    <div className="text-gray-800 font-semibold">{student.student_name}</div>
                    <div className="text-sm text-gray-600">{student.performance_message}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-gray-800 font-bold">{student.score} pts</div>
                  <div className={`text-sm ${getPerformanceColor(student.accuracy_percentage)}`}>
                    {student.accuracy_percentage.toFixed(1)}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Student Performance Details */}
        <div className="bg-white rounded-lg p-6 border border-green-200 shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <TrendingUp className="text-green-600" size={24} />
            <h3 className="text-lg font-semibold text-gray-800">Student Performance</h3>
          </div>
          <div className="space-y-4">
            {dashboardData?.leaderboard?.map((student, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-4 border border-green-100">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-800 font-semibold">{student.student_name}</span>
                  <span className="text-gray-600">{student.questions_answered} questions</span>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Correct: </span>
                    <span className="text-green-600">{student.correct_responses}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Avg Time: </span>
                    <span className="text-green-600">{student.avg_response_time_seconds.toFixed(1)}s</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Question-wise Analysis */}
      <div className="bg-white rounded-lg p-6 border border-green-200 shadow-sm mb-8">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Question-wise Analysis</h3>
        <div className="space-y-4">
          {dashboardData?.question_summaries?.map((question, index) => (
            <div key={index} className="bg-gray-50 rounded-lg p-4 border border-green-100">
              <div className="flex justify-between items-start mb-2">
                <div className="flex-1">
                  <div className="text-gray-800 font-semibold mb-1">Question {question.question_number}</div>
                  <div className="text-gray-700 text-sm mb-2">{question.question}</div>
                  <div className="text-xs text-gray-600">Correct Answer: {question.correct_answer}</div>
                </div>
                <div className="text-right ml-4">
                  <div className={`text-lg font-bold ${getPerformanceColor(question.percentage_correct)}`}>
                    {question.percentage_correct}%
                  </div>
                  <div className="text-xs text-gray-600">correct</div>
                </div>
              </div>
              
              {question.student_responses.length > 0 && (
                <div className="mt-3 pt-3 border-t border-green-200">
                  <div className="text-sm text-gray-600 mb-2">Student Responses:</div>
                  {question.student_responses.map((response, idx) => (
                    <div key={idx} className="flex items-center justify-between text-sm">
                      <span className="text-gray-800">{response.student_name}</span>
                      <div className="flex items-center gap-2">
                        <span className={response.is_correct ? 'text-green-600' : 'text-red-600'}>
                          {response.option_chosen}
                        </span>
                        <span className="text-gray-600">({response.response_time.toFixed(1)}s)</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Raw Data (collapsed by default) */}
      <div className="bg-white rounded-lg p-6 border border-green-200 shadow-sm">
        <details>
          <summary className="text-lg font-semibold text-gray-800 mb-4 cursor-pointer hover:text-green-600">
            Raw Dashboard Data (Click to expand)
          </summary>
          <pre className="bg-gray-50 p-4 rounded-lg text-gray-800 overflow-auto max-h-96 text-sm border border-green-100">
            {JSON.stringify(dashboardData, null, 2)}
          </pre>
        </details>
      </div>
    </div>
  );
};

export default EngagementDashboard;