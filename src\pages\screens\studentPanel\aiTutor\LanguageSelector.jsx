import React, { useState } from 'react';
import axios from 'axios';

function LanguageSelector({ processId, subject, onComplete }) {
  const [selectedLanguage, setSelectedLanguage] = useState('english');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const endpoints = {
    Mathematics: 'https://sasthra.in/mathapi/explain_slides',
    Chemistry:'https://sasthra.in/chemapi/explain_slides',
    Physics:'https://sasthra.in/physics/explain_slides',
    Biology:'https://sasthra.in/bioapi/explain_slides',
  };

  const handleStartClass = async () => {
    console.log('🔍 Starting handleStartClass...');
    const apiUrl = endpoints[subject];
    console.log('📚 Selected Subject:', subject);
    console.log('🌐 API URL:', apiUrl);

    if (!apiUrl) {
      const errMsg = `No endpoint defined for subject: ${subject}`;
      console.warn('⚠️', errMsg);
      setMessage(errMsg);
      return;
    }

    setLoading(true);
    setMessage('');
    console.log('⏳ Sending request...');

    try {
      const payload = {
        process_selector_id: processId,
        language: selectedLanguage,
      };

      console.log('📦 Payload:', payload);

      const response = await axios.post(apiUrl, payload);

      console.log('✅ API Response:', response);

      if (response.status === 200) {
        console.log('🎉 Class started successfully!');
        setMessage('Class started successfully!');
        // Assuming the response contains the URL for the PDF
        const pdfUrl = response.data.url || 'https://example.com/default.pdf'; // Adjust based on actual response structure
        onComplete(pdfUrl); // Call onComplete with the URL
      } else {
        console.warn('❌ Failed to start class. Status:', response.status);
        setMessage('Failed to start class.');
      }
    } catch (error) {
      console.error('🚨 API Error:', error);
      setMessage('Error starting class.');
    } finally {
      console.log('🧹 Cleaning up, setting loading false');
      setLoading(false);
    }
  };

  const handleLanguageChange = (e) => {
    const lang = e.target.value;
    console.log('🌍 Language changed to:', lang);
    setSelectedLanguage(lang);
  };

  return (
    <div className="max-w-md mx-auto bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-white/10">
      <h3 className="text-xl font-semibold text-white mb-4">Subject: {subject}</h3>

      <label htmlFor="language" className="block text-gray-300 mb-2">
        Select Language:
      </label>

      <select
        id="language"
        value={selectedLanguage}
        onChange={handleLanguageChange}
        className="w-full p-2 rounded-lg bg-gray-900/50 text-white border border-white/10 focus:outline-none focus:border-blue-500 mb-4"
      >
        <option value="english">English</option>
        <option value="thanglish">Tamil + English</option>
        <option value="teluglish">Telugu + English</option>
        <option value="kanglish">Kannada + English</option>
        <option value="manglish">Malayalam + English</option>
        <option value="hinglish">Hindi + English</option>
      </select>

      <button
        onClick={handleStartClass}
        disabled={loading}
        className={`w-full p-3 rounded-lg text-white font-semibold transition-colors duration-200 ${
          loading ? 'bg-gray-600 cursor-not-allowed' : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700'
        }`}
      >
        {loading ? (
          <div className="flex items-center justify-center">
            <svg className="animate-spin h-5 w-5 mr-2 text-white" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z" />
            </svg>
            Starting class...
          </div>
        ) : (
          'Start Class'
        )}
      </button>

      {message && (
        <p className={`mt-4 text-center ${message.includes('success') ? 'text-green-400' : 'text-red-400'}`}>
          {message}
        </p>
      )}
    </div>
  );
}

export default LanguageSelector;