import React, { useEffect } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { useLazyGetOnboardAssessmentServiceQuery } from '../dashboard.slice';

const OnboardAssessmentDashboard = () => {
  const [OnboardAssessment] = useLazyGetOnboardAssessmentServiceQuery();

  useEffect(() => {
    handleGetOnboardAssessment();
  }, [OnboardAssessment]);

  const handleGetOnboardAssessment = async () => {
    try {
      const res = await OnboardAssessment({ userId: sessionStorage.userId }).unwrap();
      console.log('Onboarding assessment:', res);
    } catch (error) {
      console.error('Error fetching onboarding assessment:', error);
    }
  };

  const chartOptions = {
    chart: {
      type: 'column'
    },
    title: {
      text: 'Quiz Performance Overview'
    },
    xAxis: {
      categories: ['Quiz'],
      title: {
        text: null
      }
    },
    yAxis: {
      min: 0,
      title: {
        text: 'Number of Questions'
      }
    },
    tooltip: {
      shared: true,
      pointFormat:
        '<span style="color:{series.color}">\u25CF</span> {series.name}: <b>{point.y}</b><br/>'
    },
    plotOptions: {
      column: {
        grouping: true,
        shadow: false,
        borderWidth: 0
      }
    },
    series: [
      {
        name: 'Correct Answers',
        data: [4],
        color: '#28a745'
      },
      {
        name: 'Incorrect Answers',
        data: [6],
        color: '#dc3545'
      },
      {
        name: 'Total Questions',
        data: [10],
        color: '#007bff'
      }
    ]
  };

  return (
    <div>
      <HighchartsReact highcharts={Highcharts} options={chartOptions} />
    </div>
  );
};

export default OnboardAssessmentDashboard;
