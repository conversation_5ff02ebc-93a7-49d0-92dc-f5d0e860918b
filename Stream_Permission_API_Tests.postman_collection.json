{"info": {"_postman_id": "stream-permission-api-tests", "name": "Stream Permission API Tests", "description": "Test collection for the new stream permission system", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8012", "type": "string"}, {"key": "session_id", "value": "test_session_123", "type": "string"}, {"key": "student_id", "value": "student_123", "type": "string"}, {"key": "faculty_id", "value": "faculty_456", "type": "string"}, {"key": "teacher_id", "value": "teacher_789", "type": "string"}, {"key": "request_id", "value": "", "type": "string"}], "item": [{"name": "1. Get Active Streams", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/active-streams", "host": ["{{base_url}}"], "path": ["active-streams"]}}, "response": []}, {"name": "2. Start Enhanced Stream (Teacher)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"session_id\": \"{{session_id}}\",\n  \"teacher_id\": \"{{teacher_id}}\",\n  \"teacher_name\": \"Test Teacher\",\n  \"quality\": \"medium\"\n}"}, "url": {"raw": "{{base_url}}/api/enhanced-stream/start", "host": ["{{base_url}}"], "path": ["api", "enhanced-stream", "start"]}}, "response": []}, {"name": "3. Request Stream Permission (Student)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"session_id\": \"{{session_id}}\",\n  \"student_id\": \"{{student_id}}\",\n  \"faculty_id\": \"{{faculty_id}}\",\n  \"message\": \"Please allow me to join the Physics live class. I missed the center session today.\"\n}"}, "url": {"raw": "{{base_url}}/api/stream-permission-request", "host": ["{{base_url}}"], "path": ["api", "stream-permission-request"]}}, "response": []}, {"name": "4. Check Permission Status (Student)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/stream-permission-status/{{student_id}}/{{session_id}}", "host": ["{{base_url}}"], "path": ["api", "stream-permission-status", "{{student_id}}", "{{session_id}}"]}}, "response": []}, {"name": "5. Get Faculty Permission Requests", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/stream-permission-requests/{{faculty_id}}", "host": ["{{base_url}}"], "path": ["api", "stream-permission-requests", "{{faculty_id}}"]}}, "response": []}, {"name": "6. Approve Permission Request (Faculty)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"request_id\": \"{{request_id}}\",\n  \"action\": \"approve\",\n  \"faculty_id\": \"{{faculty_id}}\",\n  \"response_message\": \"Approved! You can join the stream now.\"\n}"}, "url": {"raw": "{{base_url}}/api/stream-permission-response", "host": ["{{base_url}}"], "path": ["api", "stream-permission-response"]}}, "response": []}, {"name": "7. Reject Permission Request (Faculty)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"request_id\": \"{{request_id}}\",\n  \"action\": \"reject\",\n  \"faculty_id\": \"{{faculty_id}}\",\n  \"response_message\": \"Sorry, this session is full. Please join the next one.\"\n}"}, "url": {"raw": "{{base_url}}/api/stream-permission-response", "host": ["{{base_url}}"], "path": ["api", "stream-permission-response"]}}, "response": []}, {"name": "8. Try Join Stream Without Permission (Should Fail)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"session_id\": \"{{session_id}}\",\n  \"user_id\": \"{{student_id}}\",\n  \"user_name\": \"Test Student\",\n  \"user_role\": \"student\"\n}"}, "url": {"raw": "{{base_url}}/api/livekit/join", "host": ["{{base_url}}"], "path": ["api", "livekit", "join"]}}, "response": []}, {"name": "9. <PERSON><PERSON> After Approval (Should Work)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"session_id\": \"{{session_id}}\",\n  \"user_id\": \"{{student_id}}\",\n  \"user_name\": \"Test Student\",\n  \"user_role\": \"student\"\n}"}, "url": {"raw": "{{base_url}}/api/livekit/join", "host": ["{{base_url}}"], "path": ["api", "livekit", "join"]}}, "response": []}, {"name": "10. Faculty Join Stream (Should Work Without Permission)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"session_id\": \"{{session_id}}\",\n  \"user_id\": \"{{faculty_id}}\",\n  \"user_name\": \"Test Faculty\",\n  \"user_role\": \"faculty\"\n}"}, "url": {"raw": "{{base_url}}/api/livekit/join", "host": ["{{base_url}}"], "path": ["api", "livekit", "join"]}}, "response": []}]}