'use client';
import { useState, useEffect, useRef } from 'react';
import {
  useDoubtSolverServiceMutation,
  useFeedbackDoubtSolverServiceMutation,
  useYoutubeSearchServiceMutation
} from './problemSolver.Slice';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router';
import Latex from 'react-latex';
import YouTubeVideos from './YouTubeVidoes';
import VideoCallButton from './VideoCallButton';
import robotImg from '../../../../assets/Voice_assistance_gif.gif';
import ReactMarkdown from 'react-markdown';
import 'katex/dist/katex.min.css';
import { InlineMath, BlockMath } from 'react-katex';
import {
  FaHistory,
  FaTrash,
  FaPaperPlane,
  FaMicrophone,
  FaImage,
  FaStop,
  FaArrowLeft,
  FaRobot,
  FaThumbsUp,
  FaThumbsDown,
  FaUpload,
  FaVideo,
  FaPlay,
  FaTimes,
  FaYoutube
} from 'react-icons/fa';

const ProblemSolver = () => {
  const navigate = useNavigate();
  const [userId, setUserId] = useState('');
  const [text, setText] = useState('');
  const [audio, setAudio] = useState(null);
  const [image, setImage] = useState(null);
  const [mode, setMode] = useState('tutor');
  const [language, setLanguage] = useState('English');
  const [response, setResponse] = useState([]);
  const [audioUrl, setAudioUrl] = useState('');
  const [youtubeResults, setYoutubeResults] = useState([]);
  const [error, setError] = useState('');
  const [feedbackMessage, setFeedbackMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [activeInput, setActiveInput] = useState(null);
  const [particles, setParticles] = useState([]);
  const [morphState, setMorphState] = useState('idle');
  const [energyLevel, setEnergyLevel] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);
  const [responseHistory, setResponseHistory] = useState([]);
  const [reset, setReset] = useState(false);
  const [includeHistory, setIncludeHistory] = useState(true);
  const [showVideoCall, setShowVideoCall] = useState(false);
  const [isAudioPlaying, setIsAudioPlaying] = useState(false);
  const [showYouTube, setShowYouTube] = useState(false);
  const [aiThinking, setAiThinking] = useState(false);

  const chatContainerRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const audioInputRef = useRef(null);
  const imageInputRef = useRef(null);
  const audioRef = useRef(null);

  const languageMap = {
    English: 'english',
    'Hindi+English': 'hinglish',
    'Telugu+English': 'tenglish',
    'Malayalam+English': 'manglish',
    'Kannada+English': 'kanglish',
    'Tamil+English': 'tanglish'
  };

  const [doubtSolver, { isLoading: isDoubtSolverLoading, error: doubtSolverError }] =
    useDoubtSolverServiceMutation();
  const [feedbackDoubtSolver] = useFeedbackDoubtSolverServiceMutation();
  const [youtubeSearch, { isLoading: isYoutubeLoading, error: youtubeError }] =
    useYoutubeSearchServiceMutation();

  // Auto-scroll function
  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      setTimeout(() => {
        chatContainerRef.current.scrollTo({
          top: chatContainerRef.current.scrollHeight,
          behavior: 'smooth'
        });
      }, 100);
    }
  };

  // Initialize user_id
  useEffect(() => {
    let storedUserId = sessionStorage.getItem('user_id');
    if (!storedUserId) {
      storedUserId = 'student_' + Math.random().toString(36).substr(2, 9);
      sessionStorage.setItem('user_id', storedUserId);
    }
    setUserId(storedUserId);
  }, []);

  // Animate particles
  useEffect(() => {
    const newParticles = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      x: Math.random() * window.innerWidth,
      y: Math.random() * window.innerHeight,
      size: Math.random() * 3 + 1,
      speed: Math.random() * 2 + 0.5,
      color: `rgba(${Math.random() > 0.5 ? '59, 130, 246' : '139, 92, 246'}, ${Math.random() * 0.4 + 0.1})`
    }));
    setParticles(newParticles);

    const interval = setInterval(() => {
      setParticles((prev) =>
        prev.map((particle) => ({
          ...particle,
          x: (particle.x + particle.speed) % window.innerWidth,
          y: particle.y + Math.sin(Date.now() * 0.002 + particle.id) * 1
        }))
      );
    }, 50);
    return () => clearInterval(interval);
  }, []);

  // AI Thinking effect
  useEffect(() => {
    if (isDoubtSolverLoading || isYoutubeLoading) {
      setAiThinking(true);
      const interval = setInterval(() => {
        setEnergyLevel((prev) => (prev + 10) % 100);
      }, 150);
      return () => clearInterval(interval);
    } else {
      setAiThinking(false);
      setEnergyLevel(0);
    }
  }, [isDoubtSolverLoading, isYoutubeLoading]);

  // Recording timer
  useEffect(() => {
    let timer;
    if (isRecording) {
      timer = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [isRecording]);

  // Audio playback control
  useEffect(() => {
    if (response.length > 0 && audioUrl && hasUserInteracted) {
      if (audioRef.current) {
        audioRef.current.pause();
      }
      audioRef.current = new Audio(audioUrl);
      audioRef.current
        .play()
        .then(() => {
          console.log('Audio playback started');
          setIsAudioPlaying(true);
        })
        .catch((err) => {
          console.error('Audio playback failed:', err);
          setError('Audio playback failed. Please check if the audio URL is valid or try again.');
        });
      audioRef.current.onended = () => {
        setIsAudioPlaying(false);
      };
    }
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
        setIsAudioPlaying(false);
      }
    };
  }, [audioUrl, response, hasUserInteracted]);

  // Auto-scroll when response changes or AI is thinking
  useEffect(() => {
    scrollToBottom();
  }, [response, aiThinking]);

  // Auto-scroll when AI starts thinking
  useEffect(() => {
    if (aiThinking) {
      scrollToBottom();
    }
  }, [aiThinking]);

  // Delete uploaded files
  const handleDeleteAudio = () => {
    setAudio(null);
    if (activeInput === 'audio') {
      setActiveInput(null);
    }
  };

  const handleDeleteImage = () => {
    setImage(null);
    if (activeInput === 'image') {
      setActiveInput(null);
    }
  };

  // Start/stop recording
  const handleRecord = async () => {
    setHasUserInteracted(true);
    if (!isRecording) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        mediaRecorderRef.current = new MediaRecorder(stream, { mimeType: 'audio/webm' });
        const chunks = [];
        mediaRecorderRef.current.ondataavailable = (e) => {
          if (e.data.size > 0) {
            chunks.push(e.data);
          }
        };
        mediaRecorderRef.current.onstop = () => {
          const blob = new Blob(chunks, { type: 'audio/webm' });
          const audioFile = new File([blob], `recorded_audio_${Date.now()}.webm`, {
            type: 'audio/webm'
          });
          setAudio(audioFile);
          stream.getTracks().forEach((track) => track.stop());
          mediaRecorderRef.current = null;
        };
        mediaRecorderRef.current.start();
        setIsRecording(true);
        setActiveInput('audio');
        setMorphState('expanding');
        setEnergyLevel(50);
      } catch (err) {
        setError(
          'Failed to access microphone. Please allow microphone access in your browser settings.'
        );
      }
    } else {
      if (mediaRecorderRef.current) {
        mediaRecorderRef.current.stop();
        setIsRecording(false);
        setRecordingTime(0);
        setMorphState('idle');
        setEnergyLevel(0);
      }
    }
  };

  // Handle audio upload
  const handleAudioUpload = (e) => {
    setHasUserInteracted(true);
    const file = e.target.files[0];
    if (file && !isRecording) {
      setAudio(file);
      setActiveInput('audio');
      setMorphState('upload');
      setEnergyLevel(50);
      setTimeout(() => setMorphState('idle'), 800);
    } else if (isRecording) {
      setError('Cannot upload audio while recording is in progress.');
    }
  };

  // Handle image upload
  const handleImageUpload = (e) => {
    setHasUserInteracted(true);
    const file = e.target.files[0];
    if (file) {
      setImage(file);
      setActiveInput('image');
      setMorphState('upload');
      setEnergyLevel(50);
      setTimeout(() => setMorphState('idle'), 800);
    }
  };

  // Submit to backend
  const handleSubmit = async () => {
    setText('');
    setHasUserInteracted(true);
    if (!userId) {
      setError('User ID not found. Please refresh the page or log in again.');
      return;
    }
    if (!text && !audio && !image) {
      setError('Please provide a text, audio, or image input.');
      return;
    }

    setMorphState('processing');
    setEnergyLevel(100);
    const formData = new FormData();
    if (text) formData.append('text', text);
    if (audio) formData.append('audio', audio);
    if (image) formData.append('image', image);
    const currentMode = mode || 'tutor';
    const currentLanguage = languageMap[language] || 'english';
    formData.append('mode', currentMode);
    formData.append('language', currentLanguage);
    formData.append('user_id', userId);
    formData.append('reset', reset.toString());
    formData.append('include_history', 'true');

    try {
      setError('');
      setAudio(null);
      setImage(null);
      setFeedbackMessage('');
      console.log('Submitting with formData:', Object.fromEntries(formData));
      const [doubtResult, youtubeResult] = await Promise.all([
        doubtSolver(formData).unwrap(),
        youtubeSearch(formData).unwrap()
      ]);

      // Process doubt solver response
      const responseContent = doubtResult.response?.response || doubtResult.response || '';
      setResponse((prev) => [
        ...prev,
        { type: 'user', content: text || (audio ? 'Audio Query' : 'Image Query') },
        { type: 'bot', content: responseContent }
      ]);
      setAudioUrl(`data:audio/mp3;base64,${doubtResult.audio_base64}`);

      // Process YouTube results
      setYoutubeResults(youtubeResult.videos || []);

      // Update history from doubtResult
      if (doubtResult.history) {
        const serverHistory = [];
        for (let i = 0; i < doubtResult.history.length; i++) {
          if (doubtResult.history[i].role === 'user') {
            const nextMessage = doubtResult.history[i + 1];
            serverHistory.push({
              question: doubtResult.history[i].content || 'Query',
              response:
                nextMessage && nextMessage.role === 'assistant' ? nextMessage.content || '' : ''
            });
          }
        }
        console.log('Updated history:', serverHistory);
        setResponseHistory(serverHistory);
      }

      setMorphState('celebrating');
      // Clear the text field after successful submission
      setText('');
      setAudio(null);
      setImage(null);
      setActiveInput(null);
      setReset(false);
      setTimeout(() => {
        setMorphState('idle');
        setEnergyLevel(0);
      }, 3000);
    } catch (err) {
      console.error('Submit error:', err);
      setError(
        err.data?.detail || 'Something went wrong. Please check your input or try again later.'
      );
      setMorphState('idle');
      setEnergyLevel(0);
    }
  };

  // Handle feedback
  const handleFeedback = async (satisfied) => {
    setHasUserInteracted(true);
    try {
      const result = await feedbackDoubtSolver({ user_id: userId, satisfied }).unwrap();
      setFeedbackMessage(result.message);
      if (!satisfied && result.ticket_id) {
        setFeedbackMessage(`${result.message} Ticket ID: ${result.ticket_id}`);
      }
    } catch (err) {
      setFeedbackMessage('Failed to submit feedback. Please try again.');
    }
  };

  // Handle history item click
  const handleHistoryClick = (historyItem) => {
    setResponse((prev) => [
      ...prev,
      { type: 'user', content: historyItem.question },
      { type: 'bot', content: historyItem.response }
    ]);
    setAudioUrl('');
    setFeedbackMessage('');
    setShowHistory(false);
  };

  // Handle reset history
  const handleResetHistory = async () => {
    setHasUserInteracted(true);
    setReset(true);
    setResponseHistory([]);
    setResponse([]);
    setFeedbackMessage('History cleared.');
    try {
      const formData = new FormData();
      formData.append('user_id', userId);
      formData.append('reset', 'true');
      formData.append('include_history', 'true');
      formData.append('mode', 'tutor');
      formData.append('language', 'english');
      const result = await doubtSolver(formData).unwrap();
      console.log('Reset history response:', result);
      if (result.history) {
        const serverHistory = [];
        for (let i = 0; i < result.history.length; i++) {
          if (result.history[i].role === 'user') {
            const nextMessage = result.history[i + 1];
            serverHistory.push({
              question: result.history[i].content || 'Query',
              response:
                nextMessage && nextMessage.role === 'assistant' ? nextMessage.content || '' : ''
            });
          }
        }
        console.log('Updated history after reset:', serverHistory);
        setResponseHistory(serverHistory);
      }
    } catch (err) {
      console.error('Reset history error:', err);
      setError(err.data?.detail || 'Failed to reset history on server. Please try again.');
    }
  };

  // Format recording time
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  const cleanLatexText = (text) => {
    let cleaned = text
      .replace(/"(\$.*?\$|\$\$.*?\$\$)"/g, '$1') // Remove quotes around LaTeX
      .replace(/"([^"]*?)"/g, (match, p1) => {
        if (p1.includes('$') || p1.includes('=') || p1.match(/^[a-zA-Z0-9\s+\-*/().]+$/)) {
          return p1.trim();
        }
        return match;
      })
      .replace(/\s+(?=\\\w+)/g, '') // Remove space before LaTeX commands
      .replace(/\.(\s*)$/g, '') // Remove trailing dot
      .replace(/\boxed\s*\(/g, '\\boxed{') // Fix boxed syntax
      .replace(/\\boxed\s*\{([^}]*)$/gm, '\\boxed{$1}') // Ensure boxed closes
      .replace(/([^$])\s*(\$[^\$]*\$)\s*/g, '$1 $2') // Space around inline math
      .replace(/(\$\$[^$]*\$\$)/g, (match) => match.trim()) // Trim display math
      .replace(/\\frac\s*{([^}]*?)}\s*{([^}]*?)}/g, '\\frac{$1}{$2}') // Fix fractions
      .replace(/\\sqrt\s*{([^}]*?)}/g, '\\sqrt{$1}') // Fix square roots
      .trim();

    // Remove spaces in operations
    cleaned = cleaned.replace(/(\d)\s*([+\-*/])\s*(\d)/g, '$1$2$3');
    return cleaned;
  };

  const getYouTubeVideoId = (url) => {
    const urlParams = new URLSearchParams(new URL(url).search);
    return urlParams.get('v') || url.split('v=')[1]?.split('&')[0] || url.split('/').pop();
  };

  const formatMessage = (message) => {
    if (message.type === 'user') {
      return (
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          className="flex justify-end mb-6">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-3xl rounded-br-lg px-6 py-4 max-w-xs shadow-xl">
            <p className="text-sm font-medium">{message.content}</p>
          </div>
        </motion.div>
      );
    }

    if (typeof message.content === 'string') {
      try {
        const cleanedContent = cleanLatexText(message.content);
        return (
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex justify-start mb-6">
            <div className="flex items-start space-x-4">
              <motion.div
                animate={{
                  scale: aiThinking ? [1, 1.1, 1] : 1,
                  rotate: aiThinking ? [0, 5, -5, 0] : 0
                }}
                transition={{ duration: 2, repeat: aiThinking ? Number.POSITIVE_INFINITY : 0 }}
                className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg">
                <FaRobot className="text-white text-lg" />
              </motion.div>
              <div className="bg-white border border-gray-100 rounded-3xl rounded-bl-lg px-6 py-4 max-w-md shadow-xl">
                <div className="space-y-2">{renderMessageText(cleanedContent)}</div>
              </div>
            </div>
          </motion.div>
        );
      } catch (e) {
        console.error('Rendering Error:', e, 'Content:', message.content);
        return (
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex justify-start mb-6">
            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center flex-shrink-0">
                <FaRobot className="text-white text-lg" />
              </div>
              <div className="bg-red-50 border border-red-200 rounded-3xl rounded-bl-lg px-6 py-4 max-w-md shadow-xl">
                <p className="text-red-600 font-medium">⚠️ Error: Equation render failed!</p>
              </div>
            </div>
          </motion.div>
        );
      }
    }

    if (typeof message.content === 'object' && message.content.dialogue) {
      try {
        return (
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex justify-start mb-6">
            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg">
                <FaRobot className="text-white text-lg" />
              </div>
              <div className="bg-white border border-gray-100 rounded-3xl rounded-bl-lg px-6 py-4 max-w-md shadow-xl">
                <div className="space-y-4">
                  {message.content.dialogue.map((turn, index) => (
                    <div
                      key={index}
                      className="border-l-4 border-gradient-to-b from-blue-400 to-purple-500 pl-4">
                      <p className="text-sm">
                        <span
                          className={`font-bold text-lg ${
                            turn.speaker === 'Teacher' ? 'text-blue-700' : 'text-green-700'
                          }`}>
                          {turn.speaker}:
                        </span>{' '}
                        <span className="text-gray-800 font-medium">
                          {renderMessageText(cleanLatexText(turn.utterance))}
                        </span>
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        );
      } catch (e) {
        console.error('Dialogue Rendering Error:', e, 'Content:', message.content);
        return (
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex justify-start mb-6">
            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center flex-shrink-0">
                <FaRobot className="text-white text-lg" />
              </div>
              <div className="bg-red-50 border border-red-200 rounded-3xl rounded-bl-lg px-6 py-4 max-w-md shadow-xl">
                <p className="text-red-600 font-medium">⚠️ Error: Dialogue render failed.</p>
              </div>
            </div>
          </motion.div>
        );
      }
    }

    return (
      <motion.div
        initial={{ opacity: 0, x: -50 }}
        animate={{ opacity: 1, x: 0 }}
        className="flex justify-start mb-6">
        <div className="flex items-start space-x-4">
          <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center flex-shrink-0">
            <FaRobot className="text-white text-lg" />
          </div>
          <div className="bg-red-50 border border-red-200 rounded-3xl rounded-bl-lg px-6 py-4 max-w-md shadow-xl">
            <p className="text-red-600 font-medium">⚠️ Error: Response display failed.</p>
          </div>
        </div>
      </motion.div>
    );
  };

const renderMessageText = (text) => {
  const paragraphs = text.split(/\n\n/).filter((p) => p.trim());
  return paragraphs.map((paragraph, paragraphIndex) => {
    // Handle LaTeX blocks separately
    if (paragraph.includes('\\[') || paragraph.includes('\\]')) {
      return (
        <div key={paragraphIndex} className="mb-3 last:mb-0">
          <Latex>{paragraph.trim()}</Latex>
        </div>
      );
    }

    // Use ReactMarkdown for Markdown content with custom components
    return (
      <div key={paragraphIndex} className="mb-4 last:mb-0">
        <ReactMarkdown
          components={{
            // Custom rendering for paragraphs to handle steps or lists
            p: ({ node, ...props }) => {
              const match = node.children[0]?.value?.match(/^###\s+\d+\.\s+\*\*.+\*\*:/);
              if (match) {
                const [fullMatch, numberAndText] = node.children[0].value.match(/^###\s+(\d+\.\s+\*\*.+\*\*):/);
                const content = node.children[0].value.replace(fullMatch, '').trim();
                return (
                  <div className="flex items-start space-x-2 mb-2">
                    <h3 className="text-md font-semibold text-indigo-700 min-w-[80px]">
                      {numberAndText}:
                    </h3>
                    <span className="text-gray-700">{content}</span>
                  </div>
                );
              }
              return <p className="text-gray-700 mb-2 ml-8 whitespace-pre-line" {...props} />;
            },
            // Custom styling for headings
            h2: ({ node, ...props }) => (
              <h2 className="text-lg font-bold text-purple-700 mb-2" {...props} />
            ),
            h3: ({ node, ...props }) => (
              <h3 className="text-md font-semibold text-indigo-700 mb-2" {...props} />
            ),
            // Ensure strong (bold) text is styled
            strong: ({ node, ...props }) => (
              <strong className="text-blue-800 font-bold" {...props} />
            ),
            // Custom list item styling for bullet points
            li: ({ node, ...props }) => (
              <li className="text-gray-700 ml-8 mb-1" {...props} />
            ),
          }}
        >
          {paragraph.trim()}
        </ReactMarkdown>
      </div>
    );
  });
};


  // Add CSS to style the markdown content
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden">
      {/* Animated Background Particles */}
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full pointer-events-none"
          style={{
            left: particle.x,
            top: particle.y,
            width: particle.size,
            height: particle.size,
            backgroundColor: particle.color
          }}
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.2, 0.8, 0.2]
          }}
          transition={{
            duration: 4,
            repeat: Number.POSITIVE_INFINITY,
            delay: particle.id * 0.3
          }}
        />
      ))}

      {/* Enhanced Top Bar with YouTube Video Indicator */}
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="absolute top-0 left-0 right-0 z-20 p-4 flex items-center justify-between">
        <motion.button
          whileHover={{ scale: 1.1, rotate: -5 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => navigate('/sasthra')}
          className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white shadow-xl hover:shadow-2xl transition-all duration-300">
          <FaArrowLeft className="text-lg" />
        </motion.button>

        {/* Center - YouTube Video Indicator */}
        <AnimatePresence>
          {youtubeResults.length > 0 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8, y: -20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: -20 }}
              className="flex items-center space-x-2 bg-gradient-to-r from-red-500 to-pink-600 text-white px-4 py-2 rounded-full shadow-xl cursor-pointer"
              onClick={() => setShowYouTube(true)}
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}>
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}>
                <FaYoutube className="text-lg" />
              </motion.div>
              <div className="text-center">
                <p className="text-xs font-bold">📺 Related Videos Available!</p>
                <p className="text-xs opacity-90">
                  View {youtubeResults.length} videos according to your content
                </p>
              </div>
              <motion.div
                animate={{ scale: [1, 1.3, 1] }}
                transition={{ duration: 1.5, repeat: Number.POSITIVE_INFINITY }}
                className="w-2 h-2 bg-white rounded-full"
              />
            </motion.div>
          )}
        </AnimatePresence>

        <motion.button
          whileHover={{ scale: 1.1, rotate: 5 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => setShowHistory(!showHistory)}
          className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center text-white shadow-xl hover:shadow-2xl transition-all duration-300 relative">
          <FaHistory className="text-lg" />
          {responseHistory.length > 0 && (
            <motion.span
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
              className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full text-xs text-white flex items-center justify-center font-bold">
              {responseHistory.length}
            </motion.span>
          )}
        </motion.button>
      </motion.div>

      {/* Main Chat Container */}
      <div className="pt-20 pb-6 px-4 h-screen flex flex-col">
        {/* Chat Messages Area */}
        <div
          ref={chatContainerRef}
          className="flex-1 overflow-y-auto px-4 py-6 space-y-4"
          style={{ maxHeight: 'calc(100vh - 200px)' }}>
          {/* Welcome Message */}
          {response.length === 0 && (
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex flex-col items-center justify-center h-full text-center space-y-6">
              <img
                src={robotImg || '/placeholder.svg'}
                alt="Robot"
                className="w-46 h-36 object-contain"
              />

              <div className="space-y-3">
                <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  SPARKIT BOT
                </h2>
                <p className="text-gray-600 text-lg max-w-md">
                  Your intelligent learning companion is ready to help! Ask me anything about your
                  studies.
                </p>
              </div>
            </motion.div>
          )}

          {/* Chat Messages */}
          <AnimatePresence mode="popLayout">
            {response.map((message, index) => (
              <div key={index}>
                {formatMessage(message)}
                {message.type === 'bot' && index === response.length - 1 && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex justify-start ml-16 space-x-3 mb-6">
                    {/* <motion.button
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleFeedback(true)}
                      className="px-4 py-2 bg-gradient-to-r from-green-400 to-green-600 rounded-full text-white text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2">
                      <FaThumbsUp className="text-sm" />
                      <span>Helpful</span>
                    </motion.button> */}
                    {/* <motion.button
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleFeedback(false)}
                      className="px-4 py-2 bg-gradient-to-r from-red-400 to-red-600 rounded-full text-white text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2">
                      <FaThumbsDown className="text-sm" />
                      <span>Not helpful</span>
                    </motion.button> */}
                    {audioUrl && (
                      <motion.button
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => {
                          if (audioRef.current) {
                            if (isAudioPlaying) {
                              audioRef.current.pause();
                              setIsAudioPlaying(false);
                            } else {
                              audioRef.current.currentTime = 0;
                              audioRef.current
                                .play()
                                .then(() => {
                                  console.log('Audio playback restarted');
                                  setIsAudioPlaying(true);
                                })
                                .catch((err) => {
                                  console.error('Audio playback failed:', err);
                                  setError('Audio playback failed. Please try again.');
                                });
                            }
                          }
                        }}
                        className="px-4 py-2 bg-gradient-to-r from-gray-400 to-gray-600 rounded-full text-white text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2">
                        {isAudioPlaying ? (
                          <FaStop className="text-sm" />
                        ) : (
                          <FaPlay className="text-sm" />
                        )}
                        <span>{isAudioPlaying ? 'Stop Audio' : 'Play Audio'}</span>
                      </motion.button>
                    )}
                  </motion.div>
                )}
              </div>
            ))}
          </AnimatePresence>

          {/* AI Thinking Animation */}
          {aiThinking && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              className="flex justify-start mb-6">
              <div className="flex items-start space-x-4">
                <motion.div
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 360]
                  }}
                  transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
                  className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                  <FaRobot className="text-white text-lg" />
                </motion.div>
                <div className="bg-white border border-gray-100 rounded-3xl rounded-bl-lg px-6 py-4 shadow-xl">
                  <div className="flex items-center space-x-3">
                    <span className="text-gray-600 font-medium">AI is thinking</span>
                    <div className="flex space-x-1">
                      {[0, 1, 2].map((i) => (
                        <motion.div
                          key={i}
                          animate={{
                            scale: [1, 1.5, 1],
                            opacity: [0.3, 1, 0.3]
                          }}
                          transition={{
                            duration: 1.5,
                            repeat: Number.POSITIVE_INFINITY,
                            delay: i * 0.2
                          }}
                          className="w-2 h-2 bg-gradient-to-r from-purple-500 to-blue-600 rounded-full"
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </div>

        {/* File Attachments Display */}
        <AnimatePresence>
          {(audio || image) && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mx-4 mb-4 flex flex-wrap gap-3">
              {audio && (
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className="flex items-center bg-gradient-to-r from-blue-100 to-purple-100 rounded-2xl px-4 py-3 shadow-lg">
                  <FaMicrophone className="text-blue-600 mr-3 text-lg" />
                  <span className="text-blue-800 font-medium mr-3">
                    {audio.name} {isRecording && `(${formatTime(recordingTime)})`}
                  </span>
                  <motion.button
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={handleDeleteAudio}
                    className="text-red-500 hover:text-red-700 p-1 rounded-full hover:bg-red-100 transition-all duration-200">
                    <FaTrash className="text-sm" />
                  </motion.button>
                </motion.div>
              )}
              {image && (
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className="flex items-center bg-gradient-to-r from-green-100 to-blue-100 rounded-2xl px-4 py-3 shadow-lg">
                  <FaImage className="text-green-600 mr-3 text-lg" />
                  <span className="text-green-800 font-medium mr-3">{image.name}</span>
                  <motion.button
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={handleDeleteImage}
                    className="text-red-500 hover:text-red-700 p-1 rounded-full hover:bg-red-100 transition-all duration-200">
                    <FaTrash className="text-sm" />
                  </motion.button>
                </motion.div>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Enhanced Input Area */}
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="mx-4 bg-white rounded-3xl shadow-2xl border border-gray-100 p-4">
          {/* Settings Row */}
          <div className="flex items-center justify-between mb-4 pb-4 border-b border-gray-100">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-600">Mode:</span>
                <select
                  className="text-sm bg-gradient-to-r from-blue-50 to-purple-50 border border-gray-200 rounded-xl px-3 py-1 text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  value={mode}
                  onChange={(e) => setMode(e.target.value)}
                  disabled={isDoubtSolverLoading || isYoutubeLoading}>
                  <option value="text">Text</option>
                  <option value="tutor">Tutor</option>
                  <option value="conversation">Chat</option>
                </select>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-600">Language:</span>
                <select
                  className="text-sm bg-gradient-to-r from-blue-50 to-purple-50 border border-gray-200 rounded-xl px-3 py-1 text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  value={language}
                  onChange={(e) => setLanguage(e.target.value)}
                  disabled={isDoubtSolverLoading || isYoutubeLoading}>
                  {Object.keys(languageMap).map((lang) => (
                    <option key={lang} value={lang}>
                      {lang}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <motion.button
              whileHover={{ scale: 1.1, rotate: 5 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setShowYouTube(!showYouTube)}
              className="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-600 rounded-full flex items-center justify-center text-white shadow-lg hover:shadow-xl transition-all duration-300">
              <FaVideo className="text-sm" />
            </motion.button>
          </div>

          {/* Main Input Row */}
          <div className="flex items-end space-x-3">
            <div className="flex-1">
              <textarea
                className="w-full p-4 bg-gradient-to-r from-gray-50 to-blue-50 border border-gray-200 rounded-2xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:bg-white transition-all duration-300 resize-none"
                placeholder="Ask me anything about your studies..."
                value={text}
                onChange={(e) => setText(e.target.value)}
                disabled={isDoubtSolverLoading || isYoutubeLoading}
                rows={2}
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSubmit();
                  }
                }}
              />
            </div>

            {/* Input Type Buttons */}
            <div className="flex space-x-2">
              <motion.button
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => audioInputRef.current.click()}
                disabled={isRecording || isDoubtSolverLoading || isYoutubeLoading}
                className="w-12 h-12 bg-gradient-to-r from-green-400 to-blue-500 rounded-2xl text-white shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center disabled:opacity-50"
                title="Upload Audio">
                <FaUpload className="text-lg" />
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.9 }}
                onClick={handleRecord}
                disabled={isDoubtSolverLoading || isYoutubeLoading}
                className={`w-12 h-12 rounded-2xl text-white shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center ${
                  isRecording
                    ? 'bg-gradient-to-r from-red-500 to-pink-600 animate-pulse'
                    : 'bg-gradient-to-r from-purple-500 to-indigo-600'
                }`}
                title={isRecording ? 'Stop Recording' : 'Record Audio'}>
                {isRecording ? (
                  <FaStop className="text-lg" />
                ) : (
                  <FaMicrophone className="text-lg" />
                )}
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => imageInputRef.current.click()}
                disabled={isDoubtSolverLoading || isYoutubeLoading}
                className="w-12 h-12 bg-gradient-to-r from-orange-400 to-red-500 rounded-2xl text-white shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center disabled:opacity-50"
                title="Upload Image">
                <FaImage className="text-lg" />
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.9 }}
                onClick={handleSubmit}
                disabled={isDoubtSolverLoading || isYoutubeLoading || (!text && !audio && !image)}
                className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl text-white shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center disabled:opacity-50"
                title="Send Message">
                {isDoubtSolverLoading || isYoutubeLoading ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }}
                    className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                  />
                ) : (
                  <FaPaperPlane className="text-lg" />
                )}
              </motion.button>
            </div>
          </div>

          {/* Recording Indicator */}
          <AnimatePresence>
            {isRecording && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="flex items-center justify-center mt-3 space-x-3">
                <motion.div
                  animate={{ scale: [1, 1.3, 1] }}
                  transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY }}
                  className="w-3 h-3 bg-red-500 rounded-full"
                />
                <span className="text-red-600 font-medium">
                  Recording... {formatTime(recordingTime)}
                </span>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Feedback Message */}
        <AnimatePresence>
          {feedbackMessage && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mx-4 mt-3 p-4 bg-gradient-to-r from-green-100 to-blue-100 rounded-2xl text-center shadow-lg">
              <p className="text-green-800 font-medium">{feedbackMessage}</p>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* YouTube Videos Panel */}
      <AnimatePresence>
        {showYouTube && (
          <motion.div
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            className="fixed top-0 right-0 h-full w-[800px] bg-white shadow-2xl border-l border-gray-200 z-50 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
                Related Videos
              </h3>
              <motion.button
                whileHover={{ scale: 1.1, rotate: 90 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setShowYouTube(false)}
                className="w-10 h-10 bg-gradient-to-r from-gray-100 to-gray-200 rounded-full flex items-center justify-center text-gray-600 hover:bg-gradient-to-r hover:from-red-100 hover:to-pink-100 hover:text-red-600 transition-all duration-300">
                <FaTimes className="text-lg" />
              </motion.button>
            </div>
            <YouTubeVideos
              youtubeResults={youtubeResults}
              isYoutubeLoading={isYoutubeLoading}
              getYouTubeVideoId={getYouTubeVideoId}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* History Panel */}
      <AnimatePresence>
        {showHistory && (
          <motion.div
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            className="fixed top-0 right-0 h-full w-96 bg-white shadow-2xl border-l border-gray-200 z-50 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                Question History
              </h3>
              <motion.button
                whileHover={{ scale: 1.1, rotate: 90 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setShowHistory(false)}
                className="w-10 h-10 bg-gradient-to-r from-gray-100 to-gray-200 rounded-full flex items-center justify-center text-gray-600 hover:bg-gradient-to-r hover:from-purple-100 hover:to-blue-100 hover:text-purple-600 transition-all duration-300">
                <FaTimes className="text-lg" />
              </motion.button>
            </div>
            <div className="flex-1 overflow-y-auto mb-4">
              {responseHistory.length === 0 ? (
                <div className="text-center py-12">
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
                    className="w-16 h-16 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <FaHistory className="text-3xl text-purple-400" />
                  </motion.div>
                  <p className="text-gray-500 font-medium">No questions asked yet.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {responseHistory.map((item, index) => (
                    <motion.button
                      key={index}
                      whileHover={{ scale: 1.02, x: 5 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleHistoryClick(item)}
                      className="w-full text-left p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl hover:from-purple-100 hover:to-blue-100 border border-purple-100 transition-all duration-300 shadow-sm hover:shadow-md">
                      <p className="text-sm text-gray-800 font-medium line-clamp-3">
                        {item.question.length > 80
                          ? `${item.question.slice(0, 80)}...`
                          : item.question}
                      </p>
                    </motion.button>
                  ))}
                </div>
              )}
            </div>
            <motion.button
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleResetHistory}
              disabled={isDoubtSolverLoading || isYoutubeLoading}
              className="w-full p-4 bg-gradient-to-r from-red-500 to-pink-600 rounded-2xl text-white font-bold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center space-x-3 disabled:opacity-50">
              <FaTrash className="text-lg" />
              <span>Clear History</span>
            </motion.button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Video Call Overlay */}
      <AnimatePresence>
        {showVideoCall && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ y: 50 }}
              animate={{ y: 0 }}
              className="bg-white rounded-3xl shadow-2xl p-8 max-w-md w-full">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Video Call
                </h3>
                <motion.button
                  whileHover={{ scale: 1.1, rotate: 90 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => setShowVideoCall(false)}
                  className="w-10 h-10 bg-gradient-to-r from-gray-100 to-gray-200 rounded-full flex items-center justify-center text-gray-600">
                  <FaTimes className="text-lg" />
                </motion.button>
              </div>
              <div className="flex items-center justify-center py-8">
                <VideoCallButton />
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Error Display */}
      <AnimatePresence>
        {(error || doubtSolverError || youtubeError) && (
          <motion.div
            initial={{ opacity: 0, y: -50, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -50, scale: 0.9 }}
            className="fixed top-24 left-1/2 transform -translate-x-1/2 z-50 max-w-md">
            <div className="bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-2xl p-6 shadow-2xl">
              <div className="flex items-center text-red-800">
                <motion.div
                  animate={{ rotate: [0, 10, -10, 0] }}
                  transition={{ duration: 0.5, repeat: 3 }}
                  className="text-2xl mr-4">
                  ⚠️
                </motion.div>
                <div>
                  <div className="font-bold text-lg mb-2">Oops! Something went wrong</div>
                  <p className="text-sm text-red-700">
                    {error ||
                      doubtSolverError?.data?.detail ||
                      youtubeError?.data?.detail ||
                      'Please try again in a moment.'}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Hidden File Inputs */}
      <input
        type="file"
        accept="audio/*"
        ref={audioInputRef}
        onChange={handleAudioUpload}
        className="hidden"
      />
      <input
        type="file"
        accept="image/*"
        ref={imageInputRef}
        onChange={handleImageUpload}
        className="hidden"
      />

      <style jsx>{`
        .latex-content-wrapper {
          display: block;
          max-width: 100%;
          overflow: hidden;
          line-height: 1.6;
          word-wrap: break-word;
          overflow-wrap: break-word;
        }
        .latex-content-wrapper .katex-display {
          margin: 0.5em 0;
          overflow-x: auto;
          overflow-y: hidden;
          max-width: 100%;
          padding: 2px 0;
        }
        .latex-content-wrapper .katex {
          font-size: 0.95em;
          line-height: 1.4;
          max-width: 100%;
          overflow-wrap: break-word;
        }
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        .line-clamp-3 {
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        ::-webkit-scrollbar {
          width: 6px;
        }
        ::-webkit-scrollbar-track {
          background: transparent;
        }
        ::-webkit-scrollbar-thumb {
          background: linear-gradient(to bottom, #8b5cf6, #3b82f6);
          border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(to bottom, #7c3aed, #2563eb);
        }
      `}</style>
    </div>
  );
};

export default ProblemSolver;
