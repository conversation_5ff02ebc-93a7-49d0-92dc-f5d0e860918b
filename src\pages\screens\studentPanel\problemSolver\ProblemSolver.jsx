'use client';
import { useState, useEffect, useRef } from 'react';
import {
  useDoubtSolverServiceMutation,
  useFeedbackDoubtSolverServiceMutation
} from './problemSolver.Slice';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import rehypeKatex from 'rehype-katex';
import remarkMath from 'remark-math';
import 'katex/dist/katex.min.css';
import audioImg from '../../../../assets/audio.png';
import textImg from '../../../../assets/text.png';
import imageImg from '../../../../assets/image.png';
import { FaHistory, FaTrash } from 'react-icons/fa';
import Lottie from 'lottie-react';
import botImg from '../../../../assets/botImg.json';

const ProblemSolver = () => {
  const navigate = useNavigate();
  const [userId, setUserId] = useState('');
  const [text, setText] = useState('');
  const [audio, setAudio] = useState(null);
  const [image, setImage] = useState(null);
  const [mode, setMode] = useState('tutor');
  const [language, setLanguage] = useState('English');
  const [response, setResponse] = useState(null);
  const [audioUrl, setAudioUrl] = useState('');
  const [error, setError] = useState('');
  const [feedbackMessage, setFeedbackMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [activeInput, setActiveInput] = useState(null);
  const [particles, setParticles] = useState([]);
  const [morphState, setMorphState] = useState('idle');
  const [energyLevel, setEnergyLevel] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [showTextInput, setShowTextInput] = useState(false);
  const [showAudioOptions, setShowAudioOptions] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);
  const [responseHistory, setResponseHistory] = useState([]);

  const mediaRecorderRef = useRef(null);
  const audioInputRef = useRef(null);
  const imageInputRef = useRef(null);

  const languageMap = {
    English: 'english',
    'Hindi+English': 'hinglish',
    'Telugu+English': 'tenglish',
    'Malayalam+English': 'manglish',
    'Kannada+English': 'kanglish',
    'Tamil+English': 'tanglish'
  };

  const [doubtSolver, { isLoading: isDoubtSolverLoading, error: doubtSolverError }] =
    useDoubtSolverServiceMutation();
  const [feedbackDoubtSolver] = useFeedbackDoubtSolverServiceMutation();

  // Initialize user_id, particles, and load history from sessionStorage
  useEffect(() => {
    let storedUserId = sessionStorage.getItem('user_id');
    if (!storedUserId) {
      storedUserId = 'student_' + Math.random().toString(36).substr(2, 9);
      sessionStorage.setItem('user_id', storedUserId);
    }
    setUserId(storedUserId);

    const storedHistory = sessionStorage.getItem('response_history');
    if (storedHistory) {
      setResponseHistory(JSON.parse(storedHistory));
    }

    const newParticles = Array.from({ length: 15 }, (_, i) => ({
      id: i,
      x: Math.random() * window.innerWidth,
      y: Math.random() * window.innerHeight,
      size: Math.random() * 6 + 3,
      speed: Math.random() * 2 + 0.8,
      color: `hsl(${200 + Math.random() * 40}, 60%, 70%)`
    }));
    setParticles(newParticles);
  }, []);

  // Animate particles
  useEffect(() => {
    const interval = setInterval(() => {
      setParticles((prev) =>
        prev.map((particle) => ({
          ...particle,
          x: (particle.x + particle.speed) % window.innerWidth,
          y: particle.y + Math.sin(Date.now() * 0.001 + particle.id) * 0.8
        }))
      );
    }, 50);
    return () => clearInterval(interval);
  }, []);

  // Energy level animation
  useEffect(() => {
    if (isDoubtSolverLoading) {
      const interval = setInterval(() => {
        setEnergyLevel((prev) => (prev + 8) % 100);
      }, 120);
      return () => clearInterval(interval);
    } else {
      setEnergyLevel(0);
    }
  }, [isDoubtSolverLoading]);

  // Recording timer
  useEffect(() => {
    let timer;
    if (isRecording) {
      timer = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [isRecording]);

  // Audio playback control
  useEffect(() => {
    if (response && audioUrl && hasUserInteracted) {
      const audio = new Audio(audioUrl);
      audio
        .play()
        .then(() => console.log('Audio playback started'))
        .catch((err) => {
          console.error('Audio playback failed:', err);
          setError('Audio playback failed. Please check if the audio URL is valid or try again.');
        });
      return () => audio.pause();
    }
  }, [audioUrl, response, hasUserInteracted]);

  // Delete uploaded files
  const handleDeleteAudio = () => {
    setAudio(null);
    if (activeInput === 'audio') {
      setActiveInput(null);
    }
    setShowAudioOptions(false);
  };

  const handleDeleteImage = () => {
    setImage(null);
    if (activeInput === 'image') {
      setActiveInput(null);
    }
  };

  // Start/stop recording
  const handleRecord = async () => {
    setHasUserInteracted(true);
    if (!isRecording) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        mediaRecorderRef.current = new MediaRecorder(stream, { mimeType: 'audio/webm' });
        const chunks = [];

        mediaRecorderRef.current.ondataavailable = (e) => {
          if (e.data.size > 0) {
            chunks.push(e.data);
          }
        };

        mediaRecorderRef.current.onstop = () => {
          const blob = new Blob(chunks, { type: 'audio/webm' });
          const audioFile = new File([blob], `recorded_audio_${Date.now()}.webm`, {
            type: 'audio/webm'
          });
          setAudio(audioFile);
          stream.getTracks().forEach((track) => track.stop());
          mediaRecorderRef.current = null;
        };

        mediaRecorderRef.current.start();
        setIsRecording(true);
        setActiveInput('audio');
        setMorphState('expanding');
        setEnergyLevel(50);
      } catch (err) {
        setError(
          'Failed to access microphone. Please allow microphone access in your browser settings.'
        );
        setShowAudioOptions(false);
      }
    } else {
      if (mediaRecorderRef.current) {
        mediaRecorderRef.current.stop();
        setIsRecording(false);
        setRecordingTime(0);
        setMorphState('idle');
        setEnergyLevel(0);
      }
    }
  };

  // Handle audio upload
  const handleAudioUpload = (e) => {
    setHasUserInteracted(true);
    const file = e.target.files[0];
    if (file && !isRecording) {
      setAudio(file);
      setActiveInput('audio');
      setMorphState('upload');
      setEnergyLevel(50);
      setShowAudioOptions(false);
      setTimeout(() => setMorphState('idle'), 800);
    } else if (isRecording) {
      setError('Cannot upload audio while recording is in progress.');
    }
  };

  // Handle image upload
  const handleImageUpload = (e) => {
    setHasUserInteracted(true);
    const file = e.target.files[0];
    if (file) {
      setImage(file);
      setActiveInput('image');
      setMorphState('upload');
      setEnergyLevel(50);
      setTimeout(() => setMorphState('idle'), 800);
    }
  };

  // Handle input button clicks
  const handleInputClick = (inputType) => {
    setHasUserInteracted(true);
    setActiveInput(inputType);
    setMorphState('expanding');
    if (inputType === 'text') {
      setShowTextInput(true);
      setShowAudioOptions(false);
    } else if (inputType === 'audio') {
      setShowAudioOptions(true);
      setShowTextInput(false);
    } else if (inputType === 'image') {
      imageInputRef.current.click();
      setShowTextInput(false);
      setShowAudioOptions(false);
    }
    setTimeout(() => setMorphState('idle'), 800);
  };

  // Submit to backend
  const handleSubmit = async () => {
    setHasUserInteracted(true);
    if (!userId) {
      setError('User ID not found. Please refresh the page or log in again.');
      return;
    }

    setMorphState('processing');
    setEnergyLevel(100);

    const formData = new FormData();
    if (text) formData.append('text', text);
    if (audio) formData.append('audio', audio);
    if (image) formData.append('image', image);

    const currentMode = mode || 'tutor';
    const currentLanguage = languageMap[language] || 'english';

    formData.append('mode', currentMode);
    formData.append('language', currentLanguage);
    formData.append('user_id', userId);

    try {
      setError('');
      setFeedbackMessage('');
      const result = await doubtSolver(formData).unwrap();
      setResponse(result.response);
      setAudioUrl(`data:audio/mp3;base64,${result.audio_base64}`);

      const newHistoryItem = {
        question: text || (audio ? 'Audio Query' : 'Image Query'),
        response: result.response
      };

      setResponseHistory((prev) => {
        const newHistory = [...prev, newHistoryItem];
        sessionStorage.setItem('response_history', JSON.stringify(newHistory));
        return newHistory;
      });

      setMorphState('celebrating');

      // Reset input states
      setText('');
      setAudio(null);
      setImage(null);
      setActiveInput(null);
      setShowTextInput(false);
      setShowAudioOptions(false);

      setTimeout(() => {
        setMorphState('idle');
        setEnergyLevel(0);
      }, 3000);
    } catch (err) {
      setError(
        err.data?.detail?.[0]?.msg ||
          'Something went wrong. Please check your input or try again later.'
      );
      setMorphState('idle');
      setEnergyLevel(0);
    }
  };

  // Handle feedback
  const handleFeedback = async (satisfied) => {
    setHasUserInteracted(true);
    try {
      const result = await feedbackDoubtSolver({ user_id: userId, satisfied }).unwrap();
      setFeedbackMessage(result.message);
      if (!satisfied && result.ticket_id) {
        setFeedbackMessage(`${result.message} Ticket ID: ${result.ticket_id}`);
      }
    } catch (err) {
      setFeedbackMessage('Failed to submit feedback. Please try again.');
    }
  };

  // Handle history item click
  const handleHistoryClick = (historyItem) => {
    setResponse(historyItem.response);
    setAudioUrl('');
    setFeedbackMessage('');
    setShowHistory(false);
  };

  // Format recording time
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  // Format response with LaTeX support
  const formatResponse = (response) => {
    if (response === null || response === undefined) {
      return (
        <p className="text-gray-600 italic text-center py-8">
          🤖 Ready to help! Ask me anything...
        </p>
      );
    }

    if (typeof response === 'object' && response.dialogue) {
      return (
        <div className="space-y-4">
          {response.dialogue.map((turn, index) => (
            <div key={index} className="p-3">
              <p className="mb-2">
                <strong className={turn.speaker === 'Teacher' ? 'text-blue-900' : 'text-green-800'}>
                  {turn.speaker}:
                </strong>{' '}
                <span className="text-gray-800">
                  <ReactMarkdown
                    remarkPlugins={[remarkMath]}
                    rehypePlugins={[rehypeRaw, rehypeKatex]}
                    className="inline">
                    {turn.utterance}
                  </ReactMarkdown>
                </span>
              </p>
            </div>
          ))}
        </div>
      );
    }

    return (
      <div className="prose prose-blue max-w-none text-gray-800">
        <ReactMarkdown remarkPlugins={[remarkMath]} rehypePlugins={[rehypeRaw, rehypeKatex]}>
          {response}
        </ReactMarkdown>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-blue-50 overflow-hidden">
      <style jsx>{`
        @keyframes float {
          0%,
          100% {
            transform: translateY(0px) rotate(0deg);
          }
          50% {
            transform: translateY(-20px) rotate(180deg);
          }
        }
        @keyframes pulse-ring {
          0% {
            transform: scale(0.8);
            opacity: 1;
          }
          100% {
            transform: scale(3);
            opacity: 0;
          }
        }
        @keyframes morph {
          0%,
          100% {
            border-radius: 50% 50% 50% 50%;
          }
          25% {
            border-radius: 60% 40% 60% 40%;
          }
          50% {
            border-radius: 40% 60% 40% 60%;
          }
          75% {
            border-radius: 50% 50% 30% 70%;
          }
        }
        @keyframes bounce {
          0%,
          100% {
            transform: translateY(0px) scale(1);
          }
          50% {
            transform: translateY(-15px) scale(1.1);
          }
        }
        @keyframes glow {
          0%,
          100% {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
          }
          50% {
            box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
          }
        }
        .floating {
          animation: float 8s ease-in-out infinite;
        }
        .pulse-ring {
          animation: pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
        }
        .morphing {
          animation: morph 6s ease-in-out infinite;
        }
        .bouncing {
          animation: bounce 2s ease-in-out infinite;
        }
        .glowing {
          animation: glow 3s ease-in-out infinite;
        }
        .icon-img {
          width: 28px;
          height: 28px;
        }
        .bot-img {
          width: 120px;
          height: 120px;
        }
      `}</style>

      {/* Enhanced Floating Particles */}
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full opacity-30"
          style={{
            left: particle.x,
            top: particle.y,
            width: particle.size,
            height: particle.size,
            backgroundColor: particle.color
          }}
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.3, 0.6, 0.3]
          }}
          transition={{
            duration: 4,
            repeat: Number.POSITIVE_INFINITY,
            delay: particle.id * 0.3
          }}
        />
      ))}

      {/* Corner Controls */}
      {/* Top Left - Back Button */}
      <motion.button
        whileHover={{ scale: 1.15, rotate: -5 }}
        whileTap={{ scale: 0.9 }}
        onClick={() => navigate('/sasthra')}
        className="fixed top-6 left-6 z-50 w-16 h-16 bg-blue-500 backdrop-blur-lg rounded-2xl border-2 border-blue-200 flex items-center justify-center text-white hover:bg-blue-600 transition-all duration-300 shadow-xl glowing"
        title="Back to Sasthra">
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M15 19l-7-7 7-7" />
        </svg>
      </motion.button>

      {/* Top Right - Settings */}
      <motion.div
        className="fixed top-6 right-6 z-50"
        initial={{ opacity: 0, x: 100 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.5 }}>
        <motion.button
          whileHover={{ scale: 1.15, rotate: 90 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => setShowSettings(!showSettings)}
          className="w-16 h-16 bg-blue-500 backdrop-blur-lg rounded-2xl border-2 border-blue-200 flex items-center justify-center text-white hover:bg-blue-600 transition-all duration-300 shadow-xl glowing"
          title="Settings">
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2.5}
              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2.5}
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
            />
          </svg>
        </motion.button>
        <AnimatePresence>
          {showSettings && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8, y: -20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: -20 }}
              className="bg-white/90 backdrop-blur-xl rounded-3xl p-6 border-2 border-blue-200 shadow-2xl mt-4 min-w-[280px]">
              <div className="space-y-6">
                <div>
                  <label className="block text-blue-800 text-sm font-bold mb-3">🎯 Mode</label>
                  <select
                    className="w-full p-3 bg-white border-2 border-blue-200 rounded-xl text-blue-800 font-medium focus:outline-none focus:border-blue-500"
                    value={mode || 'tutor'}
                    onChange={(e) => setMode(e.target.value)}
                    disabled={isDoubtSolverLoading}>
                    <option value="text" className="bg-white">
                      📝 Text Mode
                    </option>
                    <option value="tutor" className="bg-white">
                      👨‍🏫 Tutor Mode
                    </option>
                    <option value="conversation" className="bg-white">
                      💬 Conversation
                    </option>
                  </select>
                </div>
                <div>
                  <label className="block text-blue-800 text-sm font-bold mb-3">🌍 Language</label>
                  <select
                    className="w-full p-3 bg-white border-2 border-blue-200 rounded-xl text-blue-800 font-medium focus:outline-none focus:border-blue-500"
                    value={language || 'English'}
                    onChange={(e) => setLanguage(e.target.value)}
                    disabled={isDoubtSolverLoading}>
                    {Object.keys(languageMap).map((lang) => (
                      <option key={lang} value={lang} className="bg-white">
                        {lang}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Bottom Left - Input Controls */}
      <motion.div
        className="fixed bottom-6 left-6 z-40 flex space-x-4"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}>
        <motion.button
          whileHover={{ scale: 1.2, rotate: 10 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => handleInputClick('text')}
          className={`w-16 h-16 rounded-2xl backdrop-blur-lg border-2 border-blue-200 flex items-center justify-center shadow-xl transition-all duration-300 ${
            activeInput === 'text'
              ? 'bg-blue-600 border-blue-400 glowing text-white'
              : 'bg-blue-500 hover:bg-blue-600 text-white'
          }`}
          title="Type Question">
          <img src={textImg || '/placeholder.svg'} alt="Text Input" className="icon-img" />
        </motion.button>
        <motion.button
          whileHover={{ scale: 1.2, rotate: -10 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => handleInputClick('audio')}
          className={`w-16 h-16 rounded-2xl backdrop-blur-lg border-2 border-blue-200 flex items-center justify-center shadow-xl transition-all duration-300 ${
            activeInput === 'audio' && !isRecording
              ? 'bg-blue-600 border-blue-400 glowing text-white'
              : isRecording
                ? 'bg-red-500 border-red-300 pulse-ring text-white'
                : 'bg-blue-500 hover:bg-blue-600 text-white'
          }`}
          title="Audio Input">
          <img src={audioImg || '/placeholder.svg'} alt="Audio Input" className="icon-img" />
        </motion.button>
        <motion.button
          whileHover={{ scale: 1.2, rotate: 10 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => handleInputClick('image')}
          className={`w-16 h-16 rounded-2xl backdrop-blur-lg border-2 border-blue-200 flex items-center justify-center shadow-xl transition-all duration-300 ${
            activeInput === 'image'
              ? 'bg-blue-600 border-blue-400 glowing text-white'
              : 'bg-blue-500 hover:bg-blue-600 text-white'
          }`}
          title="Upload Image">
          <img src={imageImg || '/placeholder.svg'} alt="Image Upload" className="icon-img" />
        </motion.button>
        <input
          type="file"
          accept="audio/*"
          ref={audioInputRef}
          onChange={handleAudioUpload}
          className="hidden"
        />
        <input
          type="file"
          accept="image/*"
          ref={imageInputRef}
          onChange={handleImageUpload}
          className="hidden"
        />
      </motion.div>

      {/* Bottom Right - History */}
      <motion.div
        className="fixed bottom-6 right-6 z-40"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}>
        <motion.button
          whileHover={{ scale: 1.15, rotate: -10 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => setShowHistory(!showHistory)}
          className="w-16 h-16 bg-blue-500 backdrop-blur-lg rounded-2xl border-2 border-blue-200 flex items-center justify-center text-white hover:bg-blue-600 transition-all duration-300 shadow-xl glowing"
          title="View History">
          <FaHistory className="text-2xl" />
        </motion.button>
        <AnimatePresence>
          {showHistory && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8, y: 50 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: 50 }}
              className="bg-white/90 backdrop-blur-xl rounded-3xl p-6 border-2 border-blue-200 shadow-2xl mb-4 w-80 max-h-96 overflow-y-auto">
              <h3 className="text-blue-800 font-bold text-lg mb-4 flex items-center">
                <FaHistory className="mr-2" />
                Question History
              </h3>
              {responseHistory.length === 0 ? (
                <p className="text-blue-600 text-center py-4">📝 No questions asked yet.</p>
              ) : (
                responseHistory.map((item, index) => (
                  <motion.button
                    key={index}
                    whileHover={{ scale: 1.02, x: 5 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => handleHistoryClick(item)}
                    className="w-full text-left mb-3 p-4 bg-white rounded-xl hover:bg-blue-50 transition-all duration-200 border border-blue-200">
                    <p className="text-sm text-blue-800 font-semibold mb-1">
                      {item.question.length > 35
                        ? `${item.question.slice(0, 35)}...`
                        : item.question}
                    </p>
                    <p className="text-xs text-blue-600">
                      {typeof item.response === 'string'
                        ? item.response.slice(0, 50)
                        : 'Dialogue response'}
                      ...
                    </p>
                  </motion.button>
                ))
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Main Content Layout */}
      <div className="flex h-screen">
        {/* Left Side - Bot and Title */}
        <div className="w-1/2 flex flex-col items-center justify-center p-8 relative">
          <motion.div
            className="bg-white/80 backdrop-blur-xl rounded-3xl p-8 border-2 border-blue-200 shadow-2xl w-full max-w-2xl h-5/6"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}>
            <div className="text-blue-800 h-full flex flex-col">
              <div className="flex items-center mb-6">
                <div className="text-3xl mr-4">{response ? '🎉' : '💭'}</div>
                <div className="text-2xl font-bold">
                  {response ? 'AI Response' : 'Waiting for Your Question'}
                </div>
              </div>
              <div className="bg-white rounded-2xl p-6 flex-1 overflow-y-auto mb-6 border border-blue-200">
                {formatResponse(response)}
              </div>
              {response && (
                <div className="flex justify-center space-x-4 p-4">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleFeedback(true)}
                    className="px-6 py-3 bg-green-500 rounded-xl text-white font-bold text-sm shadow-lg hover:bg-green-600 transition-all duration-300">
                    👍 Perfect!
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleFeedback(false)}
                    className="px-6 py-3 bg-red-500 rounded-xl text-white font-bold text-sm shadow-lg hover:bg-red-600 transition-all duration-300">
                    👎 Try Again
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => {
                      setResponse(null);
                      setAudioUrl('');
                      setText('');
                      setAudio(null);
                      setImage(null);
                      setActiveInput(null);
                      setShowTextInput(false);
                      setShowAudioOptions(false);
                      setFeedbackMessage('');
                    }}
                    className="px-6 py-3 bg-gray-500 rounded-xl text-white font-bold text-sm shadow-lg hover:bg-gray-600 transition-all duration-300">
                    🗑️ Clear
                  </motion.button>
                </div>
              )}
              {feedbackMessage && (
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-4 text-center text-blue-800 font-medium bg-blue-50 rounded-xl p-3 border border-blue-200">
                  {feedbackMessage}
                </motion.p>
              )}
            </div>
          </motion.div>
        </div>

        {/* Right Side - Response Area */}
        <div className="w-1/2 p-8 flex items-center justify-center">
          {/* Bot Animation */}
          <motion.div
            className="mb-8 relative"
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3, duration: 0.8 }}>
            <div
              className={`w-40 h-40 rounded-full bg-blue-500 flex items-center justify-center shadow-2xl ${
                isDoubtSolverLoading ? 'pulse-ring' : 'floating morphing'
              }`}>
              <div className="w-32 h-32 bg-white/30 rounded-full flex items-center justify-center backdrop-blur-sm">
                <div className="w-24 h-24 bg-white/40 rounded-full flex items-center justify-center">
                  <Lottie
                    animationData={botImg}
                    loop={true}
                    className="w-40 h-40"
                    style={{ animation: 'bounce 2s ease-in-out infinite' }}
                  />
                </div>
              </div>
            </div>
            {/* Energy Ring */}
            {isDoubtSolverLoading && (
              <div className="absolute inset-0 rounded-full border-4 border-blue-400 animate-spin opacity-60"></div>
            )}
          </motion.div>

          {/* Title Section */}
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}>
            <h1 className="text-5xl font-bold text-blue-800 mb-4">AI Problem Solver</h1>
            <p className="text-xl text-blue-700 font-medium mb-6">
              Your intelligent learning companion
            </p>
            <div className="bg-white/80 backdrop-blur-lg rounded-2xl p-4 border border-blue-200 shadow-lg">
              <p className="text-blue-600 text-sm">
                💡 Type, speak, or upload images to get instant help!
              </p>
            </div>
          </motion.div>

          {/* Input Status Display */}
          {(text || audio || image) && (
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-8 bg-white/80 backdrop-blur-xl rounded-3xl p-6 border-2 border-blue-200 shadow-xl max-w-md w-full">
              <div className="text-blue-800">
                <h3 className="font-bold text-lg mb-4 text-center">📋 Current Input</h3>
                {text && (
                  <div className="flex items-center justify-between mb-3 p-3 bg-white rounded-xl border border-blue-200">
                    <div className="flex items-center">
                      <img
                        src={textImg || '/placeholder.svg'}
                        alt="Text"
                        className="w-6 h-6 mr-3"
                      />
                      <span className="text-sm font-medium">
                        {text.length > 30 ? `${text.slice(0, 30)}...` : text}
                      </span>
                    </div>
                  </div>
                )}
                {audio && (
                  <div className="flex items-center justify-between mb-3 p-3 bg-white rounded-xl border border-blue-200">
                    <div className="flex items-center">
                      <img
                        src={audioImg || '/placeholder.svg'}
                        alt="Audio"
                        className="w-6 h-6 mr-3"
                      />
                      <span className="text-sm font-medium">
                        {audio.name} {isRecording ? `(${formatTime(recordingTime)})` : ''}
                      </span>
                    </div>
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={handleDeleteAudio}
                      className="text-red-500 hover:text-red-700 p-1">
                      <FaTrash className="w-4 h-4" />
                    </motion.button>
                  </div>
                )}
                {image && (
                  <div className="flex items-center justify-between mb-3 p-3 bg-white rounded-xl border border-blue-200">
                    <div className="flex items-center">
                      <img
                        src={imageImg || '/placeholder.svg'}
                        alt="Image"
                        className="w-6 h-6 mr-3"
                      />
                      <span className="text-sm font-medium">{image.name}</span>
                    </div>
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={handleDeleteImage}
                      className="text-red-500 hover:text-red-700 p-1">
                      <FaTrash className="w-4 h-4" />
                    </motion.button>
                  </div>
                )}
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleSubmit}
                  disabled={isDoubtSolverLoading}
                  className="mt-4 px-6 py-3 bg-blue-500 rounded-2xl text-white font-bold text-sm w-full flex items-center justify-center space-x-2 shadow-lg hover:bg-blue-600 transition-all duration-300">
                  {isDoubtSolverLoading ? (
                    <>
                      <svg className="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        />
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        />
                      </svg>
                      <span>Processing...</span>
                    </>
                  ) : (
                    <>
                      <span>🚀</span>
                      <span>Analyze Now</span>
                    </>
                  )}
                </motion.button>
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Modals and Overlays */}
      {/* Audio Options Modal */}
      <AnimatePresence>
        {showAudioOptions && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50">
            <div className="bg-white/90 backdrop-blur-xl rounded-3xl p-8 border-2 border-blue-200 shadow-2xl">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-blue-800">🎤 Audio Input Options</h3>
              </div>
              <div className="flex space-x-6">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleRecord}
                  disabled={isDoubtSolverLoading}
                  className={`flex-1 px-6 py-4 rounded-2xl text-white font-bold shadow-lg transition-all duration-300 ${
                    isRecording ? 'bg-red-500 hover:bg-red-600' : 'bg-blue-500 hover:bg-blue-600'
                  }`}>
                  <img
                    src={audioImg || '/placeholder.svg'}
                    alt="Record Audio"
                    className="w-8 h-8 mx-auto mb-2"
                  />
                  {isRecording ? `Stop Recording (${formatTime(recordingTime)})` : 'Record Audio'}
                </motion.button>
                <motion.button
                  whileHover={{ scale: isRecording ? 1 : 1.05 }}
                  whileTap={{ scale: isRecording ? 1 : 0.95 }}
                  onClick={() => audioInputRef.current.click()}
                  disabled={isRecording || isDoubtSolverLoading}
                  className={`flex-1 px-6 py-4 rounded-2xl text-white font-bold shadow-lg transition-all duration-300 ${
                    isRecording
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-green-500 hover:bg-green-600'
                  }`}>
                  <img
                    src={audioImg || '/placeholder.svg'}
                    alt="Upload Audio"
                    className="w-8 h-8 mx-auto mb-2"
                  />
                  Upload Audio
                </motion.button>
              </div>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setShowAudioOptions(false)}
                className="mt-6 w-full px-6 py-3 bg-red-500 rounded-2xl text-white font-bold shadow-lg hover:bg-red-600 transition-all duration-300">
                Cancel
              </motion.button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Text Input Modal */}
      <AnimatePresence>
        {showTextInput && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 50 }}
            className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50">
            <div className="bg-white/90 backdrop-blur-xl rounded-3xl p-8 border-2 border-blue-200 shadow-2xl">
              <div className="text-center mb-6">
                <img
                  src={textImg || '/placeholder.svg'}
                  alt="Text Input"
                  className="w-16 h-16 mx-auto mb-4"
                />
                <h3 className="text-2xl font-bold text-blue-800">✍️ Ask Your Question</h3>
              </div>
              <textarea
                className="w-96 h-48 bg-white border-2 border-blue-200 rounded-2xl p-6 text-blue-800 placeholder-blue-400 focus:outline-none focus:border-blue-500 resize-none text-lg"
                placeholder="Type your question here..."
                value={text}
                onChange={(e) => setText(e.target.value)}
                disabled={isDoubtSolverLoading}
              />
              <div className="flex justify-between mt-6">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setShowTextInput(false)}
                  className="px-8 py-3 bg-red-500 rounded-2xl text-white font-bold shadow-lg hover:bg-red-600 transition-all duration-300">
                  Cancel
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleSubmit}
                  disabled={isDoubtSolverLoading}
                  className="px-10 py-3 bg-blue-500 rounded-2xl text-white font-bold flex items-center justify-center space-x-2 shadow-lg hover:bg-blue-600 transition-all duration-300">
                  {isDoubtSolverLoading ? (
                    <>
                      <svg className="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        />
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        />
                      </svg>
                      <span>Thinking...</span>
                    </>
                  ) : (
                    <>
                      <span>🚀</span>
                      <span>Solve</span>
                    </>
                  )}
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Error Display */}
      <AnimatePresence>
        {(error || doubtSolverError) && (
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50">
            <div className="bg-red-100 backdrop-blur-xl rounded-2xl p-6 border-2 border-red-200 shadow-2xl max-w-md">
              <div className="text-red-800">
                <div className="flex items-center mb-2">
                  <div className="text-2xl mr-3">⚠️</div>
                  <div className="font-bold text-lg">Error!</div>
                </div>
                <p className="text-sm">
                  {error || doubtSolverError?.data?.detail?.[0]?.msg || 'Something went wrong.'}
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ProblemSolver;
